'use client'
import { ProductCard } from "@/components/productCard";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { useProducts } from "@/hooks/useProducts";

export default function DashboardPage() {
  const { products, loading } = useProducts();
  return (
    <>
      {loading ? (
        <LoadingSpinner />
      ) : (
        products &&
        products.length > 0 &&
        products.map((product, index) => (
          <ProductCard
            key={index}
            username={product.user?.username ?? ""}
            user_avatar={product.user?.profile_picture ?? ""}
            id={product.id}
            description={product.description ?? ""}
            images={product.product_images ?? []}
            price={product.price ?? 0}
            timestamp={new Date(product.created_at)}
          />
        ))
      )}
    </>
  );
}
