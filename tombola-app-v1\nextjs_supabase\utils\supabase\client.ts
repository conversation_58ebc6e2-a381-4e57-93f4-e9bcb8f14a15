import { createBrowserClient } from "@supabase/ssr";

export const createClient = () =>
  createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
          realtime: {
              params: {
                  eventsPerSecond: 10,
              },
              workerUrl: process.env.NEXT_PUBLIC_SUPABASE_URL!.replace('http', 'ws') + '/realtime/v1'
          },
        db: {
          schema: 'public',
        },
      }
  );
