#!/bin/bash
set -e

AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
AWS_REGION=ap-southeast-2  # Change if needed

declare -a FUNCTIONS=("buy-ticket" "draw-raffle" "confirm-delivery" "release-escrow" "notifications")

for fn in "${FUNCTIONS[@]}"
do
  echo "🔨 Building $fn..."
  docker build -t $fn ./lambda-functions/$fn

  echo "📦 Tagging and pushing to ECR..."
  REPO="$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$fn"
  docker tag $fn $REPO
  aws ecr describe-repositories --repository-names "$fn" || aws ecr create-repository --repository-name "$fn"
  aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
  docker push $REPO
done

echo "🚀 Deploying with SAM..."
sam deploy --template-file template.yaml --stack-name tombola-app --capabilities CAPABILITY_IAM
