'use client'
import React, { useEffect, useState } from "react";
import { ConnectAccountOnboarding, ConnectComponentsProvider } from "@stripe/react-connect-js";
import useStripeConnect from "@/hooks/stripe/useStripeConnect";
import { createStripeConnectAccount } from "@/app/(dashboard)/upload-post/actions";

export const StripeConnectOnboarding = (): JSX.Element => {
  const [connectedAccountId, setConnectedAccountId] = useState<string | undefined>();
  const stripeConnectInstance = useStripeConnect(connectedAccountId);

  useEffect(() => {
    const initStripeAccount = async () => {

      console.log('initiating stripe onboarding')
      const { account, error } = await createStripeConnectAccount();
      console.log({account})
      if (account) {
        setConnectedAccountId(account);
      }
    };

    initStripeAccount();
  }, []);

  return (
    <div>
      {stripeConnectInstance && (
        <ConnectComponentsProvider connectInstance={stripeConnectInstance}>
          <ConnectAccountOnboarding onExit={() => {}} />
        </ConnectComponentsProvider>
      )}
    </div>
  );
};

export default StripeConnectOnboarding;