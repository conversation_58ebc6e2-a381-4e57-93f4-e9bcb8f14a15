import { stripe } from '@/utils/stripe/utils';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    
    const accountSession = await stripe.accountSessions.create({
      account: body.account,
      components: {
        account_onboarding: { enabled: true },
      }
    });

    return NextResponse.json({
      client_secret: accountSession.client_secret,
    });
  } catch (error: any) {
    console.error(
      "An error occurred when calling the Stripe API to create an account session",
      error
    );
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}