'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from 'lucide-react'
import { LoginSchema } from '@/schemas/login-schema'
import { createClient } from "@/utils/supabase/client"
import type { z } from 'zod'

type FormData = z.infer<typeof LoginSchema>

export default function LoginForm() {
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const router = useRouter()
    const supabase = createClient()

    const {register, handleSubmit, formState: {errors}} = useForm<FormData>({
        resolver: zodResolver(LoginSchema)
    })

    const onSubmit = async (data: FormData) => {
        setIsLoading(true)
        setError(null)

        try {
            const {error} = await supabase.auth.signInWithPassword({
                email: data.email,
                password: data.password,
            })

            if (error) throw error

            router.push('/dashboard')
        } catch (err: any) {
            setError(err?.message || 'Failed to login')
        } finally {
            setIsLoading(false)
        }
    }


    return (
        <div className="h-screen bg-gray-100 flex items-center justify-center p-4">
            <div className="w-full max-w-md space-y-8">
                <div>
                    <h1 className="text-2xl font-bold text-primary text-center">
                        <span className="hidden md:block">Tombola</span>
                        <span className="md:hidden">T</span>
                    </h1>
                </div>

                <Card>
                    <CardContent className="pt-6">
                        {error && (
                            <Alert variant="destructive" className="mb-6">
                            <AlertCircle className="h-4 w-4"/>
                                <AlertDescription>{error}</AlertDescription>
                            </Alert>
                        )}

                        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                            <div>
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    autoComplete="email"
                                    {...register('email')}
                                    className={errors.email ? 'border-red-500' : ''}
                                    disabled={isLoading}
                                />
                                {errors.email && (
                                    <p className="mt-1 text-sm text-red-500">{errors.email.message}</p>
                                )}
                            </div>

                            <div>
                                <Label htmlFor="password">Password</Label>
                                <Input
                                    id="password"
                                    type="password"
                                    autoComplete="current-password"
                                    {...register('password')}
                                    className={errors.password ? 'border-red-500' : ''}
                                    disabled={isLoading}
                                />
                                {errors.password && (
                                    <p className="mt-1 text-sm text-red-500">{errors.password.message}</p>
                                )}
                            </div>

                            <div className="flex items-center justify-end">
                                <Link
                                    href="/forgot-password"
                                    className="text-sm font-medium text-blue-600 hover:text-blue-500"
                                >
                                    Forgot password?
                                </Link>
                            </div>

                            <Button
                                type="submit"
                                className="w-full"
                                disabled={isLoading}
                            >
                                {isLoading ? 'Signing in...' : 'Sign in'}
                            </Button>
                        </form>
                    </CardContent>

                    <div className="p-6 bg-gray-50 rounded-b-lg text-center">
                        <p className="text-sm text-gray-600">
                            Don't have an account?{' '}
                            <Link
                                href="/register"
                                className="font-semibold text-blue-600 hover:text-blue-500"
                            >
                                Sign up
                            </Link>
                        </p>
                    </div>
                </Card>
            </div>
        </div>
    );

}
