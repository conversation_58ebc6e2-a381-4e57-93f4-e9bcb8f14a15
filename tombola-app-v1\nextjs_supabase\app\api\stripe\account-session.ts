import { stripe } from '@/utils/stripe/utils';
import { NextApiRequest, NextApiResponse } from 'next/types';

type SuccessResponse = {
  client_secret: string;
};

type ErrorResponse = {
  error: string;
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<SuccessResponse | ErrorResponse>
) {
  if (req.method === 'POST') {
    try {
      const accountSession = await stripe.accountSessions.create({
        account: req.body.account as string,
        components: {
          account_onboarding: { enabled: true },
        }
      });

      res.json({
        client_secret: accountSession.client_secret,
      });
    } catch (error) {
      console.error(
        "An error occurred when calling the Stripe API to create an account session",
        error
      );
      res.status(500);
      res.json({
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  }
}