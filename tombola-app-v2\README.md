# Tombola Raffle App - Version 2

This is the new architecture for the Tombola Raffle application, designed to maintain your existing AWS Lambda microservices while modernizing the database and frontend stack.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Native  │    │   Node.js API   │    │  AWS Lambda     │    │    Appwrite     │
│   (Web/Mobile)  │◄──►│   (Express)     │◄──►│  Functions      │◄──►│   (Database)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 Project Structure

```
tombola-app-v2/
├── README.md                    # This file
├── REWRITE_SUMMARY.md          # Complete migration overview
├── project-structure.md        # Detailed architecture documentation
├── implementation-plan.md      # Step-by-step implementation guide
│
├── aws-backend/                # Updated AWS Lambda functions
│   └── lambda_functions/
│       ├── shared/
│       │   └── appwrite_client.py  # Appwrite integration
│       └── buy_ticket/
│           └── handler.py          # Updated to use Appwrite
│
├── api-layer/                  # Node.js Express API Gateway
│   ├── package.json
│   └── src/
│       ├── app.js              # Main Express application
│       ├── routes/
│       │   └── tickets.js      # Example API routes
│       ├── services/
│       │   ├── appwrite.js     # Appwrite service layer
│       │   └── aws.js          # AWS Lambda invocation
│       └── middleware/         # Auth, validation, etc.
│
├── frontend/                   # React Native (Web-first)
│   └── package.json           # Expo + React Native Web
│
└── backend/                    # Additional backend services
    └── api-gateway/           # Alternative API gateway setup
```

## 🎯 Key Design Decisions

### ✅ What We're Keeping
- **AWS Lambda Functions** - Your proven business logic
- **Docker Containers** - Existing containerization
- **Stripe Integration** - Payment processing and escrow
- **SNS/SES** - Event-driven notifications
- **CloudFormation** - Infrastructure as Code

### 🔄 What We're Changing
- **Database**: Supabase → Self-hosted Appwrite
- **Frontend**: Next.js → React Native (web-first)
- **API Layer**: Add Node.js Express bridge
- **Authentication**: Appwrite Auth

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Docker & Docker Compose
- AWS CLI configured
- Python 3.9+ (for Lambda functions)

### 1. Setup Appwrite (Database)
```bash
# Clone and start Appwrite
git clone https://github.com/appwrite/appwrite.git appwrite-server
cd appwrite-server
docker-compose up -d

# Access Appwrite Console at http://localhost
# Create project and configure collections
```

### 2. Start API Layer
```bash
cd api-layer
npm install
cp .env.example .env
# Configure environment variables
npm run dev
```

### 3. Start React Native Frontend
```bash
cd frontend
npm install
npm run web
```

### 4. Update AWS Lambda Functions
```bash
cd aws-backend/lambda_functions
# Update environment variables in CloudFormation
# Deploy updated functions
```

## 📚 Documentation

- **[REWRITE_SUMMARY.md](./REWRITE_SUMMARY.md)** - Complete migration overview
- **[project-structure.md](./project-structure.md)** - Detailed architecture
- **[implementation-plan.md](./implementation-plan.md)** - Step-by-step guide

## 🔧 Development Workflow

1. **Database Setup**: Configure Appwrite collections
2. **Lambda Updates**: Replace Supabase client with Appwrite
3. **API Development**: Build Node.js API endpoints
4. **Frontend Development**: Create React Native screens
5. **Integration Testing**: Test complete flow
6. **Deployment**: Deploy to production

## 🌟 Benefits of V2 Architecture

### Immediate Benefits
- ✅ **Self-hosted Database**: Full control over data
- ✅ **Better Performance**: Appwrite optimizations
- ✅ **Cost Control**: No per-user pricing
- ✅ **Modern Frontend**: React Native ecosystem

### Future Benefits
- 🚀 **Mobile Apps**: Same codebase for iOS/Android
- 🚀 **Microservices Ready**: Easy to add new services
- 🚀 **Multi-tenant**: Support multiple raffle apps
- 🚀 **Real-time Features**: Live updates and notifications

## 🔄 Migration Strategy

1. **Parallel Development**: Build V2 alongside V1
2. **Gradual Migration**: Move features one by one
3. **Data Migration**: Export from Supabase, import to Appwrite
4. **Testing**: Comprehensive testing before switch
5. **Deployment**: Blue-green deployment strategy

## 📞 Support

- Check the documentation files in this directory
- Review the original V1 code in `../tombola-app-v1/`
- Follow the implementation plan for step-by-step guidance
