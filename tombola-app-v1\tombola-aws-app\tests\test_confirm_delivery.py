from lambda_functions.confirm_delivery.handler import handler

def test_confirm_delivery(monkeypatch):
    monkeypatch.setattr("lambda_functions.confirm_delivery.handler.supabase.table", lambda name: type('', (), {
        "update": lambda self, val: type('', (), {"eq": lambda self, k, v: type('', (), {"execute": lambda self: None})()})(),
        "select": lambda self, *a, **kw: type('', (), {"eq": lambda self, k, v: type('', (), {"execute": lambda self: type('', (), {"data": [{"product_sent": True, "product_delivered": True}]})()})()})(),
    })())

    monkeypatch.setattr("lambda_functions.confirm_delivery.handler.boto3.client", lambda s: type('', (), {"publish": lambda self, **kw: None})())

    result = handler({"product_id": "abc-123", "status": "product_delivered"}, {})
    assert result["statusCode"] == 200