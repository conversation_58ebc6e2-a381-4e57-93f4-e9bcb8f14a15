'use server'

import { <PERSON>ada<PERSON> } from "next"
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { redirect, notFound } from 'next/navigation'
import MessagesView from "@/views/messages/message-view"


export default async function MessagesPage({ params }: { params: any }) {
    const { slug } = await params

    if (!slug) return notFound()

    const cookieStore = await cookies()
    const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
            cookies: {
                get(name: string) {
                    return cookieStore.get(name)?.value
                },
                set(name: string, value: string, options: any) {
                    cookieStore.set({ name, value, ...options })
                },
                remove(name: string, options: any) {
                    cookieStore.set({ name, value: '', ...options })
                },
            },
        }
    )

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) redirect('/login')

    const { data: otherUser } = await supabase
        .from('user')
        .select('*')
        .eq('username', slug)
        .single()

    if (!otherUser) return notFound()

    let profileImageUrl = null
    if (otherUser.profile_picture) {
        const { data } = await supabase.storage
            .from('profile')
            .createSignedUrl(otherUser.profile_picture, 3600)

        profileImageUrl = data?.signedUrl
    }

    const { data: messages } = await supabase
        .from('messages')
        .select('*')
        .or(`sender_id.eq.${user.id},receiver_id.eq.${user.id}`)
        .or(`sender_id.eq.${otherUser.id},receiver_id.eq.${otherUser.id}`)
        .order('created_at', { ascending: true })

    const formattedMessages = await Promise.all(
        messages?.map(async (message) => {
            let signedImageUrl = null
            if (message.image_url) {
                const { data } = await supabase.storage
                    .from('messages')
                    .createSignedUrl(message.image_url, 3600)
                signedImageUrl = data?.signedUrl
            }
            return {
                ...message,
                image_url: signedImageUrl
            }
        }) || []
    )

    return (
        <MessagesView
            currentUser={user}
            otherUser={{
                ...otherUser,
                profile_picture: profileImageUrl
            }}
            initialMessages={formattedMessages}
        />
    )
}
