-- Enable RLS and set default UUIDs for each table

-- Table: users
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(255),
    email VARCHAR(255),
    password VARCHAR(255),
    profile_picture VARCHAR(255),
    bio TEXT,
    role VARCHAR(50)
);
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Table: notifications
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    message TEXT,
    read BOOLEAN,
    created_at TIMESTAMP
);
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Table: charities
CREATE TABLE charities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    business_address TEXT,
    description TEXT,
    name VA<PERSON>HA<PERSON>(255),
    logo VARCHAR(255)
);
ALTER TABLE charities ENABLE ROW LEVEL SECURITY;

-- Table: categories
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255),
    description TEXT
);
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;

-- Table: subcategories
CREATE TABLE subcategories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category_id UUID REFERENCES categories(id),
    name VARCHAR(255),
    description TEXT
);
ALTER TABLE subcategories ENABLE ROW LEVEL SECURITY;



-- Table: products
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    product_name VARCHAR(255),
    description TEXT,
    price FLOAT,
    ticket_cost FLOAT,
    ticket_count INT,
    created_at TIMESTAMP,
    raffle_drawn BOOLEAN,
    product_delivered BOOLEAN,
    category_id UUID REFERENCES categories(id),
    subcategory_id UUID REFERENCES subcategories(id)
);
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Table: tickets
CREATE TABLE tickets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    guid UUID DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    product_id UUID REFERENCES products(id),
    purchase_date TIMESTAMP
);
ALTER TABLE tickets ENABLE ROW LEVEL SECURITY;

-- Table: follows
CREATE TABLE follows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    follower_id UUID REFERENCES users(id),
    followee_id UUID REFERENCES users(id)
);
ALTER TABLE follows ENABLE ROW LEVEL SECURITY;

-- Table: comments
CREATE TABLE comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id),
    product_id UUID REFERENCES products(id),
    content TEXT,
    created_at TIMESTAMP
);
ALTER TABLE comments ENABLE ROW LEVEL SECURITY;




-- Policies

-- Users table: Allow authenticated users to read and write to their own data
CREATE POLICY "authenticated users can access their data" 
ON users 
FOR ALL
USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- Notifications table: Allow authenticated users to read and write their own notifications
CREATE POLICY "authenticated users can read their notifications"
ON notifications
FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "authenticated users can create and modify notifications"
ON notifications
FOR ALL
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Charities table: Allow authenticated users to access their own charity records
CREATE POLICY "authenticated users can access their charities"
ON charities
FOR ALL
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Tickets table: Allow public (anon) read access but restrict modifications to authenticated users
CREATE POLICY "public can read tickets"
ON tickets
FOR SELECT
TO anon
USING (true);

CREATE POLICY "authenticated users can create and manage their tickets"
ON tickets
FOR ALL
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Follows table: Allow authenticated users to create, read, and manage follow records
CREATE POLICY "authenticated users can manage follows"
ON follows
FOR ALL
USING (auth.uid() = follower_id)
WITH CHECK (auth.uid() = follower_id);

-- Products table: Allow public (anon) read access but restrict modifications to authenticated users
CREATE POLICY "public can read products"
ON products
FOR SELECT
TO anon
USING (true);

CREATE POLICY "authenticated users can create and manage their products"
ON products
FOR ALL
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Comments table: Allow authenticated users to create and manage their comments
CREATE POLICY "authenticated users can manage comments"
ON comments
FOR ALL
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Categories and Subcategories tables: Allow public (anon) read access
CREATE POLICY "public can read categories"
ON categories
FOR SELECT
TO anon
USING (true);

CREATE POLICY "public can read subcategories"
ON subcategories
FOR SELECT
TO anon
USING (true);
