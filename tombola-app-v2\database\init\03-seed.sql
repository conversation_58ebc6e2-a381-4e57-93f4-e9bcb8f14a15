-- Tombola V2 Seed Data
-- Connect to the database
\c tombola_db;

-- Insert sample categories
INSERT INTO public.category (id, name, description, sort_order) VALUES
    ('550e8400-e29b-41d4-a716-************', 'Electronics', 'Electronic devices and gadgets', 1),
    ('550e8400-e29b-41d4-a716-************', 'Fashion', 'Clothing, accessories, and fashion items', 2),
    ('550e8400-e29b-41d4-a716-************', 'Home & Garden', 'Home improvement and garden items', 3),
    ('550e8400-e29b-41d4-a716-446655440004', 'Sports & Outdoors', 'Sports equipment and outdoor gear', 4),
    ('550e8400-e29b-41d4-a716-446655440005', 'Books & Media', 'Books, movies, music, and games', 5)
ON CONFLICT (id) DO NOTHING;

-- Insert sample subcategories
INSERT INTO public.subcategory (id, category_id, name, description, sort_order) VALUES
    -- Electronics subcategories
    ('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Smartphones', 'Mobile phones and accessories', 1),
    ('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Laptops', 'Portable computers and accessories', 2),
    ('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Gaming', 'Gaming consoles and accessories', 3),
    
    -- Fashion subcategories
    ('660e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-************', 'Mens Clothing', 'Clothing for men', 1),
    ('660e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-************', 'Womens Clothing', 'Clothing for women', 2),
    ('660e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-************', 'Accessories', 'Fashion accessories', 3),
    
    -- Home & Garden subcategories
    ('660e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-************', 'Kitchen', 'Kitchen appliances and tools', 1),
    ('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Furniture', 'Home furniture', 2),
    ('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Garden Tools', 'Gardening equipment', 3)
ON CONFLICT (id) DO NOTHING;

-- Insert sample charities
INSERT INTO public.charity (id, name, description, website, charity_number, is_verified) VALUES
    ('770e8400-e29b-41d4-a716-************', 'Local Food Bank', 'Supporting families in need with food assistance', 'https://localfoodbank.org', 'CHR001', true),
    ('770e8400-e29b-41d4-a716-************', 'Animal Rescue Centre', 'Rescuing and rehoming abandoned animals', 'https://animalrescue.org', 'CHR002', true),
    ('770e8400-e29b-41d4-a716-************', 'Children Education Fund', 'Providing educational resources to underprivileged children', 'https://childreneducation.org', 'CHR003', true)
ON CONFLICT (id) DO NOTHING;

-- Insert sample auth users for testing
INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at) VALUES
    ('880e8400-e29b-41d4-a716-************', '<EMAIL>', crypt('admin123', gen_salt('bf')), NOW(), NOW(), NOW()),
    ('880e8400-e29b-41d4-a716-************', '<EMAIL>', crypt('seller123', gen_salt('bf')), NOW(), NOW(), NOW()),
    ('880e8400-e29b-41d4-a716-************', '<EMAIL>', crypt('buyer123', gen_salt('bf')), NOW(), NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Insert sample users (these will be created automatically by the trigger, but we can update them)
INSERT INTO public."user" (id, username, email, role, bio) VALUES
    ('880e8400-e29b-41d4-a716-************', 'admin', '<EMAIL>', 'admin', 'System administrator'),
    ('880e8400-e29b-41d4-a716-************', 'seller1', '<EMAIL>', 'user', 'Product seller and raffle creator'),
    ('880e8400-e29b-41d4-a716-************', 'buyer1', '<EMAIL>', 'user', 'Raffle enthusiast and ticket buyer')
ON CONFLICT (id) DO UPDATE SET
    username = EXCLUDED.username,
    bio = EXCLUDED.bio,
    role = EXCLUDED.role;

-- Insert sample products
INSERT INTO public.product (id, user_id, category_id, subcategory_id, product_name, description, price, ticket_cost, ticket_count, product_images) VALUES
    ('990e8400-e29b-41d4-a716-************', 
     '880e8400-e29b-41d4-a716-************', 
     '550e8400-e29b-41d4-a716-************', 
     '660e8400-e29b-41d4-a716-************',
     'iPhone 15 Pro', 
     'Brand new iPhone 15 Pro in Space Black with 256GB storage. Includes original packaging and accessories.', 
     999.99, 
     5.00, 
     200,
     ARRAY['https://example.com/iphone1.jpg', 'https://example.com/iphone2.jpg']),
    
    ('990e8400-e29b-41d4-a716-************', 
     '880e8400-e29b-41d4-a716-************', 
     '550e8400-e29b-41d4-a716-************', 
     '660e8400-e29b-41d4-a716-************',
     'MacBook Air M3', 
     'Latest MacBook Air with M3 chip, 16GB RAM, 512GB SSD. Perfect for work and creative projects.', 
     1299.99, 
     10.00, 
     130,
     ARRAY['https://example.com/macbook1.jpg', 'https://example.com/macbook2.jpg']),

    ('990e8400-e29b-41d4-a716-************', 
     '880e8400-e29b-41d4-a716-************', 
     '550e8400-e29b-41d4-a716-446655440004', 
     NULL,
     'Premium Gym Set', 
     'Complete home gym setup including dumbbells, resistance bands, and yoga mat.', 
     299.99, 
     3.00, 
     100,
     ARRAY['https://example.com/gym1.jpg', 'https://example.com/gym2.jpg'])
ON CONFLICT (id) DO NOTHING;

-- Insert sample tickets (simulate some purchases)
INSERT INTO public.ticket (id, user_id, product_id, ticket_number) VALUES
    ('aa0e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-************', 1),
    ('aa0e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-************', 2),
    ('aa0e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-************', 1)
ON CONFLICT (id) DO NOTHING;

-- Insert sample comments
INSERT INTO public.comment (id, user_id, product_id, content) VALUES
    ('bb0e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-************', 'Great product! Really hoping to win this one.'),
    ('bb0e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', '990e8400-e29b-41d4-a716-************', 'The MacBook looks amazing. Good luck everyone!')
ON CONFLICT (id) DO NOTHING;

-- Insert sample notifications
INSERT INTO public.notification (id, user_id, title, message, type) VALUES
    ('cc0e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', 'Welcome to Tombola!', 'Thank you for joining our raffle platform. Start exploring products and buy your first tickets!', 'info'),
    ('cc0e8400-e29b-41d4-a716-************', '880e8400-e29b-41d4-a716-************', 'Product Listed', 'Your iPhone 15 Pro raffle has been successfully listed and is now live!', 'success')
ON CONFLICT (id) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_category_sort_order ON public.category(sort_order);
CREATE INDEX IF NOT EXISTS idx_subcategory_sort_order ON public.subcategory(sort_order);
CREATE INDEX IF NOT EXISTS idx_charity_verified ON public.charity(is_verified) WHERE is_verified = true;
CREATE INDEX IF NOT EXISTS idx_product_status ON public.product(status);
CREATE INDEX IF NOT EXISTS idx_product_user_id ON public.product(user_id);
CREATE INDEX IF NOT EXISTS idx_ticket_product_id ON public.ticket(product_id);
CREATE INDEX IF NOT EXISTS idx_ticket_user_id ON public.ticket(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_user_unread ON public.notification(user_id, read) WHERE read = false;
