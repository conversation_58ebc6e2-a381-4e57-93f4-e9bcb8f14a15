import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { Type } from '@sinclair/typebox';
import { authMiddleware } from '../middleware/auth';
import { supabaseService } from '../services/supabase';

export default async function ticketRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
) {
  // Get user's tickets
  fastify.get('/my-tickets', {
    preHandler: [authMiddleware],
    schema: {
      response: {
        200: Type.Object({
          tickets: Type.Array(Type.Object({
            id: Type.String(),
            ticket_number: Type.Number(),
            purchase_date: Type.String(),
            is_winner: Type.Boolean(),
            product: Type.Object({
              id: Type.String(),
              name: Type.String(),
              image_url: Type.Union([Type.String(), Type.Null()]),
              status: Type.String(),
            }),
          })),
        }),
      },
    },
  }, async (request, reply) => {
    if (!request.user) {
      throw new Error('User not found');
    }

    const tickets = await supabaseService.getUserTickets(request.user.id);

    reply.send({
      tickets: tickets || [],
    });
  });

  // Purchase ticket (will call AWS Lambda)
  fastify.post('/purchase', {
    preHandler: [authMiddleware],
    schema: {
      body: Type.Object({
        product_id: Type.String(),
        quantity: Type.Number({ minimum: 1, maximum: 10 }),
        payment_method_id: Type.String(),
      }),
      response: {
        200: Type.Object({
          message: Type.String(),
          tickets: Type.Array(Type.Object({
            id: Type.String(),
            ticket_number: Type.Number(),
          })),
          payment_intent_id: Type.String(),
        }),
      },
    },
  }, async (request, reply) => {
    // This will be implemented to call AWS Lambda buy_ticket function
    reply.send({
      message: 'Ticket purchase initiated',
      tickets: [],
      payment_intent_id: 'pi_placeholder',
    });
  });
}
