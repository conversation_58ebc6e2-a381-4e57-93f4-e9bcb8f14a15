"use client";
import { CategoryWithSubcategories } from "@/types";
import { createClient } from "@/utils/supabase/client";
import { useEffect, useState } from "react";

/**
 * Custom hook to fetch and filter products by categoryId
 * @param 
 * @returns { category, loading, error }
 */
export function useCategory(categoryId?: string) {
  const [category, setCategory] = useState<CategoryWithSubcategories>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    const fetchCategory = async () => {
      setLoading(true);
      setError(null);
      try {
        const { data, error } = await supabase
          .from("category")
          .select("*, subcategories:subcategory(*)")
          .eq("id", categoryId)
          .single();

        if (error) throw error;
        setCategory(data);
      } catch (error: any) {
        console.error("Failed to fetch category:", error.message);
        setError(error.message || "An unexpected error occurred.");
      } finally {
        setLoading(false);
      }
    };

    fetchCategory();
  }, []);

  return { category, loading, error };
}
