import { stripe } from '@/utils/stripe/utils';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const account = await stripe.accounts.create({
      controller: {
        stripe_dashboard: {
          type: "none",
        },
        fees: {
          payer: "application"
        },
        losses: {
          payments: "application"
        },
        requirement_collection: "application",
      },
      capabilities: {
        transfers: { requested: true }
      },
      country: "AU",
    });

    return NextResponse.json({ account: account.id });
  } catch (error: any) {
    console.error('An error occurred when calling the Stripe API to create an account:', error);
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}