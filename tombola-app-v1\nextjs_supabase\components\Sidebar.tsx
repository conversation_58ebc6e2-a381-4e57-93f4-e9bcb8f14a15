"use client";

import Link from "next/link";
import Image from "next/image";
import {
  Home,
  Compass,
  User,
  Settings,
  Inbox,
  PlusSquare,
  LogOut,
} from "lucide-react";
import { Button } from "./ui/button";
import { createClient } from "@/utils/supabase/client";
import { useRouter } from "next/navigation";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

type SidebarProps = {
  userProfile: {
    profile_picture: string | null;
  };
};

export function Sidebar({ userProfile }: SidebarProps) {
  const supabase = createClient();
  const router = useRouter();

  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    if (!error) {
      router.push("/login");
    }
  };

  const ProfileElement = () => (
    <Avatar className="h-8 w-8 md:h-6 md:w-6">
      <AvatarImage
        src={userProfile.profile_picture || undefined}
        alt="Profile"
      />
      <AvatarFallback>
        <User className="h-4 w-4" />
      </AvatarFallback>
    </Avatar>
  );

  return (
    <>
      <aside className="sm:hidden fixed bottom-0 left-0 right-0 bg-white border-t flex justify-around items-center h-16 px-4">
        <Link href="/" className="text-gray-700 hover:text-black">
          <Home className="h-6 w-6" />
        </Link>
        <Link href="/explore" className="text-gray-700 hover:text-black">
          <Compass className="h-6 w-6" />
        </Link>
        <Button size="icon" className="rounded-full">
          <PlusSquare className="h-6 w-6" />
        </Button>
        <Link href="/profile" className="text-gray-700 hover:text-black">
          <ProfileElement />
        </Link>
        <Link href="/inbox" className="text-gray-700 hover:text-black">
          <Inbox className="h-6 w-6" />
        </Link>
      </aside>
      <aside className="hidden sm:flex fixed left-0 top-0 md:w-64 w-20 bg-white h-screen border-r flex flex-col justify-between p-4 overflow-y-auto transition-all">
        <div className="space-y-6">
          <h1 className="text-2xl font-bold text-primary text-center">
            <span className="hidden md:block">Tombola</span>
            <span className="md:hidden">T</span>
          </h1>
          <nav className="space-y-4">
            <Link
              href="/"
              className="flex justify-center md:justify-start items-center md:space-x-2 text-gray-700 hover:text-black"
            >
              <Home className="h-8 w-8 md:h-6 md:w-6" />
              <span className="invisible w-0 md:visible md:w-auto">Home</span>
            </Link>
            <Link
              href="/explore"
              className="flex justify-center md:justify-start items-center md:space-x-2 text-gray-700 hover:text-black"
            >
              <Compass className="h-8 w-8 md:h-6 md:w-6" />
              <span className="invisible w-0 md:visible md:w-auto">
                Explore
              </span>
            </Link>
            <Link
              href="/profile"
              className="flex justify-center md:justify-start items-center md:space-x-2 text-gray-700 hover:text-black"
            >
              <ProfileElement />
              <span className="invisible w-0 md:visible md:w-auto">
                Profile
              </span>
            </Link>
          </nav>
          <Link
            href="/upload-post"
            className="w-full flex justify-center md:justify-start items-center justify-center md:space-x-2 bg-orange-500 text-white hover:bg-orange-600 rounded-md p-2"
          >
            <PlusSquare className="h-8 w-8 md:h-6 md:w-6" />
            <span className="invisible w-0 md:visible md:w-auto">
              Upload Post
            </span>
          </Link>
        </div>
        <div className="space-y-4">
          <Link
            href="/settings"
            className="flex justify-center md:justify-start items-center md:space-x-2 text-gray-700 hover:text-black"
          >
            <Settings className="h-8 w-8 md:h-6 md:w-6" />
            <span className="invisible w-0 md:visible md:w-auto">Settings</span>
          </Link>
          <Link
            href="/inbox"
            className="flex justify-center md:justify-start items-center md:space-x-2 text-gray-700 hover:text-black"
          >
            <Inbox className="h-8 w-8 md:h-6 md:w-6" />
            <span className="invisible w-0 md:visible md:w-auto">Inbox</span>
          </Link>
          <Button
            variant="ghost"
            className="w-full flex justify-center md:justify-start items-center md:space-x-2 text-gray-700 hover:text-black hover:bg-gray-100 m-0 p-0"
            onClick={handleLogout}
          >
            <LogOut className="h-8 w-8 md:h-6 md:w-6" />
            <span className="invisible w-0 md:visible md:w-auto">Log out</span>
          </Button>
        </div>
      </aside>
    </>
  );
}
