'use client'

import { useEffect, useRef, useState } from 'react'
import { useForm } from 'react-hook-form'
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { User, ImageIcon, Send, X } from 'lucide-react'
import { createClient } from '@supabase/supabase-js'
import { markMessagesAsRead, sendMessage } from "@/app/(dashboard)/messages/[slug]/actions"
import { format } from "date-fns"

interface Message {
  id: string
  sender_id: string
  content?: string
  image_url?: string
  created_at: string
}

interface MessagesViewProps {
  currentUser: any
  otherUser: any
  initialMessages: Message[]
}

export default function MessagesView({
                                       currentUser,
                                       otherUser,
                                       initialMessages
                                     }: MessagesViewProps) {
  const [messages, setMessages] = useState<Message[]>(initialMessages)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  const { register, handleSubmit, reset, watch } = useForm({
    defaultValues: {
      content: '',
      image: undefined as FileList | undefined
    }
  })

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedFile(file)
    }
  }

  const fetchNewMessages = async () => {
    try {
      const lastMessage = messages[messages.length - 1]
      const lastTimestamp = lastMessage ? lastMessage.created_at : new Date(0).toISOString()

      const { data: newMessages, error } = await supabase
          .from('messages')
          .select('*')
          .or(`sender_id.eq.${currentUser.id},receiver_id.eq.${currentUser.id}`)
          .or(`sender_id.eq.${otherUser.id},receiver_id.eq.${otherUser.id}`)
          .gt('created_at', lastTimestamp)
          .order('created_at', { ascending: true })

      if (error) {
        console.error('Error fetching messages:', error)
        return
      }

      if (newMessages && newMessages.length > 0) {
        const relevantMessages = newMessages.filter(msg =>
            (msg.sender_id === currentUser.id && msg.receiver_id === otherUser.id) ||
            (msg.sender_id === otherUser.id && msg.receiver_id === currentUser.id)
        )

        const processedMessages = await Promise.all(
            relevantMessages.map(async (message) => {
              if (message.image_url) {
                const { data } = await supabase.storage
                    .from('messages')
                    .createSignedUrl(message.image_url, 3600)

                return {
                  ...message,
                  image_url: data?.signedUrl
                }
              }
              return message
            })
        )

        setMessages(prev => {
          const existingIds = new Set(prev.map(m => m.id))
          const uniqueNewMessages = processedMessages.filter(m => !existingIds.has(m.id))
          return [...prev, ...uniqueNewMessages]
        })

        const hasNewMessages = relevantMessages.some(msg => msg.sender_id === otherUser.id)
        if (hasNewMessages) {
          await markMessagesAsRead(otherUser.id)
        }
      }
    } catch (error) {
      console.error('Error in fetchNewMessages:', error)
    }
  }

  useEffect(() => {
    const pollInterval = setInterval(fetchNewMessages, 3000)
    return () => clearInterval(pollInterval)
  }, [messages.length])

  useEffect(() => {
    fetchNewMessages()
    markMessagesAsRead(otherUser.id)
  }, [otherUser.id])

  const onSubmit = async (data: any) => {
    setIsLoading(true)
    try {
      if (!data.content && !selectedFile) {
        return
      }

      let imageData
      if (selectedFile) {
        const dataTransfer = new DataTransfer()
        dataTransfer.items.add(selectedFile)
        imageData = dataTransfer.files
      }

      await sendMessage({
        content: data.content,
        image: imageData,
        receiverId: otherUser.id
      })

      reset()
      setSelectedFile(null)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }

      await fetchNewMessages()
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
      <div className="flex flex-col h-screen bg-gray-100">
        <header className="bg-white border-b border-gray-200 px-4 py-3 flex items-center">
          <Avatar className="w-10 h-10 mr-3">
            <AvatarImage src={otherUser.profile_picture} />
            <AvatarFallback>
              <User className="w-6 h-6" />
            </AvatarFallback>
          </Avatar>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">@{otherUser.username}</h2>
            <p className="text-sm text-gray-500">Online</p>
          </div>
        </header>

        <main className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message, index) => {
            const isCurrentUser = message.sender_id === currentUser.id

            return (
                <div
                    key={message.id}
                    className={`flex items-end gap-2 ${
                        isCurrentUser ? 'justify-end' : 'justify-start'
                    }`}
                >

                  <div
                      className={`group relative max-w-[70%] ${
                          isCurrentUser ? 'ml-auto' : 'mr-auto'
                      }`}
                  >

                    <div
                        className={`px-4 py-2 rounded-3xl ${
                            isCurrentUser
                                ? 'bg-blue-500 text-white'
                                : 'bg-white text-gray-800'
                        }`}
                        style={{
                          boxShadow: '0 1px 0.5px rgba(0, 0, 0, 0.13)',
                        }}
                    >
                      {message.image_url && (
                          <div className="mb-2 rounded-2xl overflow-hidden">
                            <img
                                src={message.image_url}
                                alt="Message attachment"
                                className="max-h-60 object-contain w-full"
                            />
                          </div>
                      )}
                      {message.content && (
                          <p className="break-words text-base leading-relaxed">{message.content}</p>
                      )}
                    </div>
                    <div
                        className={`flex items-center text-xs mt-2 space-x-4 ${
                            isCurrentUser ? 'justify-end' : 'justify-start'
                        }`}
                    >
                  <span className="text-gray-500 font-medium">
                    {isCurrentUser ? 'You' : `@${otherUser.username}`}
                  </span>
                    <span className="text-gray-400 ms-5">
                    {format(new Date(message.created_at), 'HH:mm')}
                  </span>
                    </div>
                  </div>
                </div>
            )
          })}
          <div ref={messagesEndRef} />
        </main>

        <footer className="bg-white border-t border-gray-200 p-4">
          <form onSubmit={handleSubmit(onSubmit)} className="flex items-center gap-2">
            <input
                type="file"
                accept="image/*"
                className="hidden"
                ref={fileInputRef}
                onChange={handleFileSelect}
            />
            <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => fileInputRef.current?.click()}
                className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full"
            >
              <ImageIcon className="w-6 h-6" />
            </Button>
            <Input
                placeholder="Send a message..."
                className="flex-1 border-none rounded-full focus:ring-2 focus:ring-blue-500 focus:border-transparent py-6"
                {...register('content')}
            />
            <Button
                type="submit"
                size="icon"
                disabled={isLoading}
                className="bg-blue-500 hover:bg-blue-600 text-white rounded-full"
            >
              <Send className="w-5 h-5" />
            </Button>
          </form>
          {selectedFile && (
              <div className="mt-2 p-2 bg-gray-100 rounded-md flex items-center justify-between">
                <p className="text-sm text-gray-600 truncate flex-1">
                  {selectedFile.name}
                </p>
                <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setSelectedFile(null)
                      if (fileInputRef.current) {
                        fileInputRef.current.value = ''
                      }
                    }}
                    className="text-gray-500 hover:text-gray-700 hover:bg-gray-200 rounded-full"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
          )}
        </footer>
      </div>
  )
}

