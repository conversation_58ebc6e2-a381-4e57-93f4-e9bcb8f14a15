'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { useRouter } from 'next/navigation'
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2 } from 'lucide-react'

// Assuming these functions are defined in your actions file
import { validateUsername, validateEmail, createUser } from '@/app/(auth-pages)/register/actions'
import {createClient} from "@/utils/supabase/client";
import {RegisterStep1Schema, RegisterStep2Schema} from "@/schemas/register-schema";


type FormData = z.infer<typeof RegisterStep1Schema> & z.infer<typeof RegisterStep2Schema>

export default function EnhancedRegistrationForm() {
    // ** States
    const [step, setStep] = useState(1)
    const [formData, setFormData] = useState<Partial<FormData>>({})
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [success, setSuccess] = useState<string | null>(null)

    // ** Hooks
    const router = useRouter()
    const supabase = createClient()

    const { register, handleSubmit, formState: { errors }, reset } = useForm<FormData>({
        resolver: zodResolver(step === 1 ? RegisterStep1Schema :  RegisterStep2Schema),
        defaultValues: formData,
    })

    const onSubmit = async (data: Partial<FormData>) => {
        setIsLoading(true)
        setError(null)
        setSuccess(null)

        try {
            if (step === 1) {
                const [isUsernameValid, isEmailValid] = await Promise.all([
                    validateUsername(data.username as string),
                    validateEmail(data.email as string),
                ])

                if (!isUsernameValid) {
                    setError('Username already taken')
                    return
                }

                if (!isEmailValid) {
                    setError('Email already registered')
                    return
                }
            }

            if (step === 2) {
                const result = await createUser({ ...formData, ...data })
                if (result.success) {
                    setSuccess('Account created successfully!')

                    const { error: signInError } = await supabase.auth.signInWithPassword({
                        email: formData.email!,
                        password: formData.password!,
                    })

                    if (signInError) {
                        throw signInError
                    }

                    router.push('/dashboard')
                }
            } else {
                setFormData({ ...formData, ...data })
                setStep(step + 1)
                reset(data)
            }
        } catch (err) {
            setError('An error occurred. Please try again.')
        } finally {
            setIsLoading(false)
        }
    }

    const stepTitles = ['Account Details', 'Profile Setup', 'Payment Information']

    return (
        <div className="min-h-screen bg-gray-100 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8">
            <div>
                <h1 className="text-2xl font-bold text-primary text-center">
                    <span className="hidden md:block">Tombola</span>
                    <span className="md:hidden">T</span>
                </h1>
            </div>

            <Card className="w-full max-w-md mx-auto mt-8">
                <CardHeader>
                    <CardTitle>{stepTitles[step - 1]}</CardTitle>
                </CardHeader>
                <CardContent>
                    {error && (
                        <Alert variant="destructive" className="mb-4">
                            <AlertCircle className="h-4 w-4"/>
                            <AlertTitle>Error</AlertTitle>
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}
                    {success && (
                        <Alert variant="default" className="mb-4 bg-green-50 border-green-200">
                            <CheckCircle2 className="h-4 w-4 text-green-600"/>
                            <AlertTitle className="text-green-800">Success</AlertTitle>
                            <AlertDescription className="text-green-700">{success}</AlertDescription>
                        </Alert>
                    )}
                    <form onSubmit={handleSubmit(onSubmit)}>
                        {step === 1 && (
                            <>
                                <div className="space-y-4">
                                    <div>
                                        <Label htmlFor="username">Username</Label>
                                        <Input id="username" {...register('username')} />
                                        {errors.username &&
                                            <p className="mt-1 text-sm text-red-600">{errors.username.message}</p>}
                                    </div>
                                    <div>
                                        <Label htmlFor="email">Email address</Label>
                                        <Input id="email" type="email" {...register('email')} />
                                        {errors.email &&
                                            <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>}
                                    </div>
                                    <div>
                                        <Label htmlFor="password">Password</Label>
                                        <Input id="password" type="password" {...register('password')} />
                                        {errors.password &&
                                            <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>}
                                    </div>
                                    <div>
                                        <Label htmlFor="confirmPassword">Confirm Password</Label>
                                        <Input id="confirmPassword" type="password" {...register('confirmPassword')} />
                                        {errors.confirmPassword &&
                                            <p className="mt-1 text-sm text-red-600">{errors.confirmPassword.message}</p>}
                                    </div>
                                </div>
                            </>
                        )}
                        {step === 2 && (
                            <>
                                <div className="space-y-4">
                                    <div>
                                        <Label htmlFor="profile_picture">Profile Picture</Label>
                                        <Input id="profile_picture" type="file"
                                               accept="image/*" {...register('profile_picture')} />
                                    </div>
                                    <div>
                                        <Label htmlFor="bio">Bio</Label>
                                        <Textarea id="bio" {...register('bio')} />
                                        {errors.bio &&
                                            <p className="mt-1 text-sm text-red-600">{errors.bio.message}</p>}
                                    </div>
                                </div>
                            </>
                        )}

                        <CardFooter className="flex justify-between mt-8 px-0">
                            {step > 1 && (
                                <Button type="button" variant="outline" onClick={() => setStep(step - 1)}
                                        disabled={isLoading}>
                                    Back
                                </Button>
                            )}
                            <Button type="submit" className={step === 1 ? 'w-full' : ''} disabled={isLoading}>
                                {isLoading ? 'Processing...' : step === 3 ? 'Complete Registration' : 'Next'}
                            </Button>
                        </CardFooter>
                    </form>
                </CardContent>
            </Card>
            <p className="mt-4 text-center text-sm text-gray-600">
                By creating an account, you agree to our{' '}
                <a href="/terms" className="font-medium text-blue-600 hover:text-blue-500">
                    Terms of Service
                </a>{' '}
                and{' '}
                <a href="/privacy" className="font-medium text-blue-600 hover:text-blue-500">
                    Privacy Policy
                </a>
            </p>
        </div>
    )
}

