'use server'

import { Database } from '@/types/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function sendMessage(data: {
  content?: string
  image?: FileList
  receiverId: string
}) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_KEY!,
        {
          cookies: {
            get(name: string) {
              return cookieStore.get(name)?.value
            },
            set(name: string, value: string, options: any) {
              cookieStore.set({name, value, ...options})
            },
            remove(name: string, options: any) {
              cookieStore.set({name, value: '', ...options})
            },
          },
        }
    )

    const {data: {user}, error: userError} = await supabase.auth.getUser()
    if (userError) throw userError
    if (!user) throw new Error('Not authenticated')

    let imageUrl = null
    if (data.image?.[0]) {
      const file = data.image[0]
      const fileExt = file.name.split('.').pop()
      const fileName = `${user.id}_${Date.now()}.${fileExt}`
      const filePath = `messages/${fileName}`

      const arrayBuffer = await file.arrayBuffer()
      const buffer = new Uint8Array(arrayBuffer)

      const {error: uploadError} = await supabase.storage
          .from('messages')
          .upload(filePath, buffer, {
            contentType: file.type,
            upsert: true
          })

      if (uploadError) throw uploadError
      imageUrl = filePath
    }

    const {data: newMessage, error: messageError} = await supabase
        .from('messages')
        .insert({
          sender_id: user.id,
          receiver_id: data.receiverId,
          content: data.content,
          image_url: imageUrl
        })
        .select()
        .single()

    if (messageError) throw messageError

    return {success: true, message: newMessage}
  } catch (error) {
    console.error('Send message error:', error)
    throw error
  }
}

export async function markMessagesAsRead(senderId: string) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set({ name, value, ...options })
          },
          remove(name: string, options: any) {
            cookieStore.set({ name, value: '', ...options })
          },
        },
      }
    )

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) throw userError
    if (!user) throw new Error('Not authenticated')

    const { error } = await supabase
      .from('messages')
      .update({ read_at: new Date().toISOString() })
      .eq('sender_id', senderId)
      .eq('receiver_id', user.id)
      .is('read_at', null)

    if (error) throw error

    return { success: true }
  } catch (error) {
    console.error('Mark messages as read error:', error)
    throw error
  }
}
