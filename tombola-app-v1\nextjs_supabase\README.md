<a href="https://demo-nextjs-with-supabase.vercel.app/">
  <h1 align="center">Tombola - made with Next.js and Supabase</h1>
</a>

<p align="center">
 The fastest way to build apps with Next.js and Supabase
</p>

<p align="center">
  <a href="#features"><strong>Features</strong></a> ·
  <a href="#demo"><strong>Demo</strong></a> ·
  <a href="#prerequisites"><strong>Prerequisites</strong></a> ·
  <a href="#to-run-supabase"><strong>To run supabase</strong></a> ·
  <a href="#to-run-the-nextjs-app"><strong>To run the nextjs app</strong></a> ·
  <a href="#feedback-and-issues"><strong>Feedback and issues</strong></a>
  <a href="#more-supabase-examples"><strong>More Examples</strong></a>
</p>
<br/>

## Features

- Works across the entire [Next.js](https://nextjs.org) stack
  - App Router
  - Pages Router
  - Middleware
  - Client
  - Server
  - It just works!
- supabase-ssr. A package to configure Supabase Auth to use cookies
- Styling with [Tailwind CSS](https://tailwindcss.com)
- Components with  [material/ui](https://mui.com/) or [shadcn/ui](https://ui.shadcn.com/)

## Demo

You can view a fully working demo at [demo-nextjs-with-supabase.vercel.app](https://demo-nextjs-with-supabase.vercel.app/).

## Prerequisites
- Install Docker on your machine -- https://www.docker.com/get-started/
- Install supabase CL -- https://supabase.com/docs/guides/local-development/cli/getting-started?queryGroups=platform&platform=windows 
- Install node version >20 (Currenntly using 23.1.0)

## To run supabase
supabase start --ignore-health-check (using this flag because at the moment the vector DB doesn't work with supabase throwing an error)

## To run the nextjs app 
npm run dev 

## To deploy lambda function
- Ensure that the aws cli is installed and all the AWS tools modules if using windows
- (Get-ECRLoginCommand).Password | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com
- docker build -t tombola/dockerimage .
- docker tag tombola/dockerimage:latest ************.dkr.ecr.us-east-1.amazonaws.com/tombola/dockerimage:latest
- docker push ************.dkr.ecr.us-east-1.amazonaws.com/tombola/dockerimage:latest

## Helpful tips
- If your solution is not working and it says your port is taken. 
- Run the following CMD's 
  - net stop winnat
  - supabase start 
  - net start winnat
- If you are trying to create migrations from the local db to code use the following:
  - supabase db diff --schema public --local --use-migra --file "file_name"
- If you need to create types in code from the supabase DB run the following: 
  - supabase gen types --lang=typescript --local > types/supabase.ts
- If you need to sync your local DB with the supabase cloud db run the following: 
  - supabase login
  - supabase link --project-ref "{supabase_cloud_project_id}"
  - supabase db push

## Feedback and issues

Please file feedback and issues over on the [Supabase GitHub org](https://github.com/supabase/supabase/issues/new/choose).

## More Supabase examples

- [Next.js Subscription Payments Starter](https://github.com/vercel/nextjs-subscription-payments)
- [Cookie-based Auth and the Next.js 13 App Router (free course)](https://youtube.com/playlist?list=PL5S4mPUpp4OtMhpnp93EFSo42iQ40XjbF)
- [Supabase Auth and the Next.js App Router](https://github.com/supabase/supabase/tree/master/examples/auth/nextjs)

## Helpful for Deployment to Supabase
https://supabase.com/docs/guides/local-development/overview 
