from lambda_functions.notifications.handler import handler
import json

def test_notifications_winner(monkeypatch):
    monkeypatch.setattr("lambda_functions.notifications.handler.boto3.client", lambda s: type('', (), {"send_email": lambda self, **kw: None})())
    event = {
        "Records": [
            {
                "Sns": {
                    "Subject": "WinnerSelected",
                    "Message": json.dumps({"product_id": "abc-123", "winner_email": "<EMAIL>"})
                }
            }
        ]
    }
    result = handler(event, {})
    assert result["statusCode"] == 200