# Tombola Raffle App - Version 1 (Original)

This folder contains the original tombola raffle application with:
- Next.js + Supabase frontend
- AWS Lambda backend with Docker containers
- Stripe payment integration

## Original Structure:
- `nextjs_supabase/` - Next.js frontend with Supabase integration
- `tombola-aws-app/` - AWS Lambda functions and CloudFormation
- `supabase/` - Supabase configuration and migrations

This version is preserved for reference and potential rollback needs.
