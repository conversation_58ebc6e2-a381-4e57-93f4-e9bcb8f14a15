# Tombola V2 - Docker Development Setup

This document provides comprehensive instructions for setting up and running the Tombola V2 application using Docker containers.

## 🏗️ Architecture Overview

The Docker setup includes the following services:

- **Supabase Database** - PostgreSQL with Supabase extensions and complete schema
- **Supabase Studio** - Web-based database GUI and management interface
- **Supabase Kong** - API Gateway for Supabase services
- **Supabase Auth** - Authentication service with JWT support
- **Supabase PostgREST** - Auto-generated REST API from database schema
- **Supabase Realtime** - Real-time subscriptions and live updates
- **Supabase Storage** - File storage with image transformations
- **Fastify API Gateway** - Custom backend API service with TypeScript
- **React Native Frontend** - Web-first frontend with Expo
- **Redis** - Caching and session storage
- **Nginx** - Reverse proxy and load balancer

## 📋 Prerequisites

- Docker Desktop (latest version)
- Docker Compose v2.0+
- Git
- 8GB+ RAM recommended
- Ports 80, 3000, 3001, 5432, 6379 available

## 🚀 Quick Start

### 1. <PERSON>lone and Navigate
```bash
cd tombola-app-v2
```

### 2. Start Development Environment
```bash
# Make scripts executable (Linux/Mac)
chmod +x scripts/*.sh

# Start all services
./scripts/dev-start.sh
```

### 3. Access Applications
- **Frontend**: http://localhost:3000
- **API**: http://localhost:3001
- **Supabase Studio (GUI)**: http://localhost:3010
- **Supabase API Gateway**: http://localhost:8000
- **Email Testing (Inbucket)**: http://localhost:9000
- **Nginx Proxy**: http://localhost:80
- **Database**: localhost:5432

## 🔧 Configuration

### Environment Variables
Copy `.env.docker` to `.env` and update values:

```bash
cp .env.docker .env
```

Key configurations:
- Database credentials
- JWT secrets
- Stripe API keys
- CORS origins
- File upload settings

### Supabase Setup
The Supabase stack is automatically initialized with:
- **Complete Database Schema**: All tables, functions, and triggers
- **Authentication System**: JWT-based auth with user management
- **Auto-generated REST API**: PostgREST creates API endpoints from schema
- **Real-time Subscriptions**: Live updates for data changes
- **File Storage**: Image and file upload with transformations
- **Database GUI**: Supabase Studio for visual database management
- **Sample Data**: Development data for testing
- **Email Testing**: Inbucket for testing email functionality

### Supabase Features Available:
- **Database Management**: Visual table editor, SQL editor, and schema browser
- **Authentication**: User signup, login, password reset, and social auth
- **API Explorer**: Test and explore auto-generated REST endpoints
- **Real-time**: Subscribe to database changes in real-time
- **Storage**: Upload and manage files with automatic image optimization
- **Functions**: Edge functions for custom server-side logic (coming soon)
- **Logs**: View real-time logs and analytics

## 📁 Project Structure

```
tombola-app-v2/
├── docker-compose.yml          # Main Docker Compose configuration
├── .env.docker                 # Environment template
├── database/
│   └── init/                   # Database initialization scripts
│       ├── 01-init.sql         # Schema creation
│       ├── 02-functions.sql    # Functions and triggers
│       └── 03-seed.sql         # Sample data
├── api-layer/
│   ├── Dockerfile              # API container configuration
│   └── src/                    # Fastify application code
├── frontend/
│   ├── Dockerfile              # Frontend container configuration
│   ├── nginx.conf              # Frontend nginx config
│   └── src/                    # React Native application
├── nginx/
│   └── nginx.conf              # Reverse proxy configuration
└── scripts/
    ├── dev-start.sh            # Start development environment
    ├── dev-stop.sh             # Stop development environment
    └── dev-clean.sh            # Clean all containers and data
```

## 🛠️ Development Commands

### Service Management
```bash
# Start all services
./scripts/dev-start.sh

# Stop all services
./scripts/dev-stop.sh

# Clean everything (removes data)
./scripts/dev-clean.sh

# View service status
docker-compose ps

# View logs
docker-compose logs -f [service_name]

# Restart specific service
docker-compose restart [service_name]
```

### Database Operations
```bash
# Connect to Supabase database
docker-compose exec supabase-db psql -U postgres -d postgres

# Run SQL file
docker-compose exec -T supabase-db psql -U postgres -d postgres < your-file.sql

# Backup database
docker-compose exec supabase-db pg_dump -U postgres postgres > backup.sql

# Restore database
docker-compose exec -T supabase-db psql -U postgres -d postgres < backup.sql

# Access Supabase Studio (recommended for GUI operations)
# Open http://localhost:3010 in your browser
```

### Supabase API Operations
```bash
# Test REST API endpoints
curl -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0" \
     http://localhost:8000/rest/v1/user

# Test authentication
curl -X POST http://localhost:8000/auth/v1/signup \
     -H "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0" \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>", "password": "password123"}'
```

### Application Development
```bash
# API shell access
docker-compose exec api sh

# Frontend shell access
docker-compose exec frontend sh

# Install new API dependencies
docker-compose exec api npm install package-name

# Install new frontend dependencies
docker-compose exec frontend npm install package-name

# Rebuild specific service
docker-compose build api
docker-compose up -d api
```

## 🔍 Debugging

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f postgres
docker-compose logs -f api
docker-compose logs -f frontend
```

### Health Checks
```bash
# API health
curl http://localhost:3001/health

# Database connection
docker-compose exec postgres pg_isready -U postgres -d tombola_db

# Redis connection
docker-compose exec redis redis-cli ping
```

### Common Issues

1. **Port Conflicts**
   - Stop services using required ports
   - Update port mappings in docker-compose.yml

2. **Database Connection Issues**
   - Check if PostgreSQL container is running
   - Verify DATABASE_URL in .env file
   - Check database logs: `docker-compose logs postgres`

3. **API Not Starting**
   - Check Node.js dependencies
   - Verify environment variables
   - Check API logs: `docker-compose logs api`

4. **Frontend Build Issues**
   - Clear node_modules: `docker-compose exec frontend rm -rf node_modules`
   - Rebuild container: `docker-compose build frontend`

## 🔒 Security Considerations

### Development Environment
- Default passwords are used (change for production)
- CORS is open for localhost
- Debug mode is enabled
- No SSL/TLS encryption

### Production Deployment
- Update all secrets and passwords
- Configure proper CORS origins
- Enable SSL/TLS with certificates
- Set NODE_ENV=production
- Configure proper logging
- Set up monitoring and alerts

## 📊 Monitoring

### Service Health
All services include health checks:
- Database: PostgreSQL ready check
- API: HTTP health endpoint
- Redis: Ping command
- Frontend: HTTP response check

### Performance Monitoring
```bash
# Container resource usage
docker stats

# Service-specific metrics
docker-compose exec api npm run metrics
```

## 🔄 Updates and Maintenance

### Updating Dependencies
```bash
# Update API dependencies
docker-compose exec api npm update

# Update frontend dependencies
docker-compose exec frontend npm update

# Rebuild containers
docker-compose build --no-cache
```

### Database Migrations
```bash
# Run new migration
docker-compose exec -T postgres psql -U postgres -d tombola_db < new-migration.sql

# Check migration status
docker-compose exec postgres psql -U postgres -d tombola_db -c "SELECT version();"
```

## 🆘 Support

For issues and questions:
1. Check the logs for error messages
2. Verify environment configuration
3. Ensure all required ports are available
4. Try cleaning and restarting: `./scripts/dev-clean.sh && ./scripts/dev-start.sh`

## 📝 Next Steps

1. **API Development**: Implement remaining endpoints in `api-layer/src/`
2. **Frontend Development**: Build React Native components in `frontend/src/`
3. **Testing**: Add comprehensive test suites
4. **Production**: Configure production Docker setup with security hardening
