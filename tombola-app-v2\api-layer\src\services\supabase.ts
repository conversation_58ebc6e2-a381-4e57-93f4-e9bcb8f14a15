import { createClient, SupabaseClient, User } from '@supabase/supabase-js';
import { config } from '../config/environment';
import { logger } from '../utils/logger';
import { Database } from '../types/database';

class SupabaseService {
  private client: SupabaseClient<Database>;
  private adminClient: SupabaseClient<Database>;

  constructor() {
    // Client for user operations (with RLS)
    this.client = createClient<Database>(
      config.supabase.url,
      config.supabase.anonKey,
      {
        auth: {
          autoRefreshToken: true,
          persistSession: false,
        },
      }
    );

    // Admin client for server operations (bypasses RLS)
    this.adminClient = createClient<Database>(
      config.supabase.url,
      config.supabase.serviceRoleKey,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );
  }

  // Get client with user context
  getClientWithAuth(accessToken: string): SupabaseClient<Database> {
    return createClient<Database>(
      config.supabase.url,
      config.supabase.anon<PERSON>ey,
      {
        global: {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    );
  }

  // Authentication methods
  async verifyToken(token: string): Promise<User | null> {
    try {
      const { data: { user }, error } = await this.client.auth.getUser(token);
      
      if (error) {
        logger.error('Token verification failed:', error);
        return null;
      }
      
      return user;
    } catch (error) {
      logger.error('Error verifying token:', error);
      return null;
    }
  }

  async signUp(email: string, password: string, userData?: Record<string, any>) {
    try {
      const { data, error } = await this.client.auth.signUp({
        email,
        password,
        options: {
          data: userData,
        },
      });

      if (error) {
        logger.error('Sign up failed:', error);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error during sign up:', error);
      throw error;
    }
  }

  async signIn(email: string, password: string) {
    try {
      const { data, error } = await this.client.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        logger.error('Sign in failed:', error);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error during sign in:', error);
      throw error;
    }
  }

  async signOut(accessToken: string) {
    try {
      const clientWithAuth = this.getClientWithAuth(accessToken);
      const { error } = await clientWithAuth.auth.signOut();

      if (error) {
        logger.error('Sign out failed:', error);
        throw error;
      }

      return { success: true };
    } catch (error) {
      logger.error('Error during sign out:', error);
      throw error;
    }
  }

  async resetPassword(email: string) {
    try {
      const { error } = await this.client.auth.resetPasswordForEmail(email, {
        redirectTo: `${config.cors.origins[0]}/reset-password`,
      });

      if (error) {
        logger.error('Password reset failed:', error);
        throw error;
      }

      return { success: true };
    } catch (error) {
      logger.error('Error during password reset:', error);
      throw error;
    }
  }

  // Database operations
  async getUser(userId: string) {
    try {
      const { data, error } = await this.adminClient
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        logger.error('Error fetching user:', error);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error in getUser:', error);
      throw error;
    }
  }

  async updateUser(userId: string, updates: Record<string, any>) {
    try {
      const { data, error } = await this.adminClient
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        logger.error('Error updating user:', error);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error in updateUser:', error);
      throw error;
    }
  }

  async getProducts(filters?: Record<string, any>) {
    try {
      let query = this.client.from('products').select('*');

      if (filters?.category) {
        query = query.eq('category_id', filters.category);
      }

      if (filters?.status) {
        query = query.eq('status', filters.status);
      }

      if (filters?.limit) {
        query = query.limit(filters.limit);
      }

      const { data, error } = await query;

      if (error) {
        logger.error('Error fetching products:', error);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error in getProducts:', error);
      throw error;
    }
  }

  async getProduct(productId: string) {
    try {
      const { data, error } = await this.client
        .from('products')
        .select('*, categories(*)')
        .eq('id', productId)
        .single();

      if (error) {
        logger.error('Error fetching product:', error);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error in getProduct:', error);
      throw error;
    }
  }

  async getUserTickets(userId: string) {
    try {
      const { data, error } = await this.adminClient
        .from('tickets')
        .select('*, products(*)')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        logger.error('Error fetching user tickets:', error);
        throw error;
      }

      return data;
    } catch (error) {
      logger.error('Error in getUserTickets:', error);
      throw error;
    }
  }

  // Get admin client for server operations
  getAdminClient(): SupabaseClient<Database> {
    return this.adminClient;
  }

  // Get regular client
  getClient(): SupabaseClient<Database> {
    return this.client;
  }
}

// Export singleton instance
export const supabaseService = new SupabaseService();
export default supabaseService;
