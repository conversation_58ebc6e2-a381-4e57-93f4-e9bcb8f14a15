def handler(event, context):
    from shared.supabase_client import supabase
    from shared.stripe_client import stripe
    from shared.logger import log
    import boto3
    import json

    try:
        # Parse SNS event
        message = json.loads(event["Records"][0]["Sns"]["Message"])
        product_id = message["product_id"]

        # Fetch product and payment intent
        product = supabase.table("product").select("*").eq("id", product_id).execute().data[0]
        payment_intent_id = product["payment_intent_id"]

        # Capture payment
        stripe.PaymentIntent.capture(payment_intent_id)

        # Update escrow status
        supabase.table("escrow").update({"status": "released"}).eq("product_id", product_id).execute()
        log("Escrow released", {"product_id": product_id})

        # Emit EscrowReleased event
        sns = boto3.client("sns")
        sns.publish(
            TopicArn="<EscrowReleasedTopic>",
            Message=json.dumps({"product_id": product_id}),
            Subject="EscrowReleased"
        )

        return {"statusCode": 200, "message": "Escrow released"}
    except Exception as e:
        log("Error in release_escrow", str(e))
        return {"statusCode": 500, "error": str(e)}
