#!/usr/bin/env node

import { config } from './config/environment';
import { logger } from './utils/logger';
import app from './app';

const start = async (): Promise<void> => {
  try {
    logger.info('Starting Tombola API Server...');
    logger.info(`Environment: ${config.NODE_ENV}`);
    logger.info(`Port: ${config.port}`);
    logger.info(`Host: ${config.host}`);

    const address = await app.listen({
      port: config.port,
      host: config.host,
    });

    logger.info(`🚀 Server listening at ${address}`);
    
    if (config.NODE_ENV === 'development') {
      logger.info(`📚 API Documentation available at: http://${config.host}:${config.port}/docs`);
    }

  } catch (error) {
    logger.error('Error starting server:', error);
    process.exit(1);
  }
};

// Handle shutdown signals
const gracefulShutdown = async (signal: string): Promise<void> => {
  logger.info(`Received ${signal}, shutting down gracefully...`);
  
  try {
    await app.close();
    logger.info('Server closed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.fatal('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.fatal('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start the server
start();
