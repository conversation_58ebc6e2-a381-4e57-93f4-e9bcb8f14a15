import Image from "next/image";
import { Button } from "./ui/button";
import { formatTimestamp } from "@/lib/utils";

interface ProductCardProps {
  id: string;
  username: string;
  user_avatar?: string;
  images?: string[];
  price: number;
  tickets_sold?: number;
  tickets_total?: number;
  timestamp: Date;
  description? : string;
}

export function ProductCard({
  id,
  username,
  user_avatar,
  images,
  price,
  tickets_sold,
  tickets_total,
  timestamp,
  description,
}: ProductCardProps) {
  return (
    <div className="bg-white border rounded-lg shadow-subtle max-w-lg mx-auto mb-4">
      <div className="flex items-center p-4 gap-4">
        <div className="relative w-10 h-10 flex-shrink-0">
          <Image
            fill
            className="rounded-full object-cover"
            src={user_avatar ?? ""}
            alt="User avatar"
          />
        </div>
        <span className="font-semibold">{username}</span>
      </div>
      <div className="relative aspect-square">
        {images && images.length > 0 && images.map((image, index) => (
          <Image
            key={index}
            src={image}
            alt="Post image"
            fill
            className="object-cover"
          />
        ))}
      </div>
      <div className="p-4">
        <div className="flex justify-between mb-4 items-start">
          <div>
            <span className="font-semibold mr-2">{username}</span>
            <span>{description}</span>
          </div>
          <div className="flex items-center gap-4">
            <div className="text-grayscale-500 text-sm mb-2 flex items-center">
              <span className="sr-only">Ticket Price:</span>
              <Image
                alt=""
                src="/assets/dollar-icon.png"
                aria-hidden="true"
                width={20}
                height={20}
              />
              {price.toLocaleString()}
            </div>
            <div className="text-grayscale-500 text-sm mb-2 flex items-center gap-1">
              <span className="sr-only">Total tickets:</span>
              <Image
                alt=""
                src="/assets/ticket-icon.png"
                aria-hidden="true"
                width={20}
                height={20}
              />
              {tickets_sold}/{tickets_total}
            </div>
          </div>
        </div>
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm text-grayscale-500">
            {formatTimestamp(timestamp)}
          </span>
          <Button variant="outline" size="sm">
            <span>BUY TICKETS</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
