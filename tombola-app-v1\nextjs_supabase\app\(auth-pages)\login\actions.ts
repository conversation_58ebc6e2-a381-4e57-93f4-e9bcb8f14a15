'use server'

import { Database } from '@/types/supabase'
import { createClient } from '@supabase/supabase-js'
import { revalidatePath } from 'next/cache'

if (!process.env.NEXT_PUBLIC_SUPABASE_URL) throw new Error('Missing SUPABASE_URL')
if (!process.env.SUPABASE_KEY) throw new Error('Missing SUPABASE_KEY')

const supabase = createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_KEY,
    {
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
    }
)

export type LoginCredentials = {
    email: string
    password: string
}

export async function login(credentials: LoginCredentials) {
    try {
        const { data, error } = await supabase.auth.signInWithPassword({
            email: credentials.email,
            password: credentials.password,
        })

        if (error) throw error

        revalidatePath('/')
        return { success: true, user: data.user }
    } catch (error) {
        console.error('Login error:', error)
        throw error
    }
}
