'use client'

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { User, MessageCircle } from 'lucide-react'
import { PhotoIcon, BookmarkIcon } from '@heroicons/react/24/outline'
import Lightbox from "yet-another-react-lightbox"
import "yet-another-react-lightbox/styles.css"
import { useState } from "react"
import { useRouter } from "next/navigation"
import {followUser, unfollowUser} from "@/app/(dashboard)/profiles/[slug]/actions";

interface ProfileData {
    id: string
    username: string
    bio: string | null
    profile_picture: string | null
    isOwnProfile: boolean
    followersCount: number
    followingCount: number
    isFollowing?: boolean
}

export default function UserProfileView({ profile }: { profile: ProfileData }) {
    const [index, setIndex] = useState(-1)
    const router = useRouter()
    const [isFollowing, setIsFollowing] = useState(profile.isFollowing)
    const [followersCount, setFollowersCount] = useState(profile.followersCount)
    const [isLoading, setIsLoading] = useState(false)

    const handleMessageClick = () => {
        router.push(`/messages/${profile.username}`)
    }

    const handleFollowClick = async () => {
        setIsLoading(true)

        try {
            const result = isFollowing
                ? await unfollowUser(profile.id)
                : await followUser(profile.id)

            if (result.success) {
                setIsFollowing(!isFollowing)
                setFollowersCount(prev => isFollowing ? prev - 1 : prev + 1)
            }
        } catch (error) {
            console.error('Follow action failed:', error)
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className="max-w-6xl mx-auto px-4 py-8">
            {/* Profile Header */}
            <div className="mb-8">
                <div className="flex flex-col sm:flex-row items-center gap-8">
                    {/* Profile Picture */}
                    <div className="shrink-0">
                        <Avatar className="w-20 h-20">
                            <AvatarImage
                                src={profile.profile_picture || undefined}
                                alt={profile.username}
                                className="object-cover"
                            />
                            <AvatarFallback className="bg-gray-100">
                                <User className="w-16 h-16 text-gray-400"/>
                            </AvatarFallback>
                        </Avatar>
                    </div>

                    {/* Profile Info */}
                    <div className="flex-1 text-center sm:text-left space-y-4">
                        <div className="flex flex-col sm:flex-row items-center gap-4">
                            <h1 className="text-xl font-semibold">@{profile.username}</h1>
                            {profile.isOwnProfile ? (
                                <Button variant="outline">Edit Profile</Button>
                            ) : (
                                <div className="flex gap-2">
                                    <Button
                                        onClick={handleFollowClick}
                                        disabled={isLoading}
                                    >
                                        {isFollowing ? 'Unfollow' : 'Follow'}
                                    </Button>
                                    <Button
                                        variant="outline"
                                        className="flex items-center gap-2"
                                        onClick={handleMessageClick}
                                    >
                                        <MessageCircle className="w-4 h-4"/>
                                        Message
                                    </Button>
                                </div>
                            )}
                        </div>

                        <div className="flex gap-4 text-sm text-gray-600">
                            <span>{followersCount} followers</span>
                            <span>{profile.followingCount} following</span>
                        </div>

                        {profile.bio && (
                            <div className="text-sm max-w-md">
                                <p className="whitespace-pre-wrap">{profile.bio}</p>
                            </div>
                        )}
                    </div>
                </div>
            </div>

        </div>
    )
}
