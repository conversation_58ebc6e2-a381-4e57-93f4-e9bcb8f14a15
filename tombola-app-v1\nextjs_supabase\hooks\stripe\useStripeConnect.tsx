'use client'

import { useState, useEffect } from "react";
import { IStripeConnectInitParams, loadConnectAndInitialize, StripeConnectInstance } from "@stripe/connect-js";

interface AccountSessionResponse {
  client_secret?: string;
  error?: string;
}

interface StripeConnectConfig {
  publishableKey: string | undefined;
  fetchClientSecret: () => Promise<string>;
  appearance: {
    overlays: 'dialog';
    variables: {
      colorPrimary: string;
    };
  };
}

export const useStripeConnect = (connectedAccountId: string | undefined): StripeConnectInstance | undefined => {
  const [stripeConnectInstance, setStripeConnectInstance] = useState<StripeConnectInstance | undefined>();

  useEffect(() => {
    if (connectedAccountId) {
      const fetchClientSecret = async (): Promise<string> => {
        const response = await fetch("/api/stripe/account-session", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            account: connectedAccountId,
          }),
        });

        const data: AccountSessionResponse = await response.json();

        if (!response.ok || data.error) {
          throw new Error(`An error occurred: ${data.error}`);
        }

        if (!data.client_secret) {
          throw new Error("No client secret returned");
        }

        return data.client_secret;
      };

      const config: IStripeConnectInitParams = {
        publishableKey: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY ?? '',
        fetchClientSecret,
        appearance: {
          overlays: "dialog",
          variables: {
            colorPrimary: "#635BFF",
          },
        },
      };

      setStripeConnectInstance(loadConnectAndInitialize(config));
    }
  }, [connectedAccountId]);

  return stripeConnectInstance;
};

export default useStripeConnect;