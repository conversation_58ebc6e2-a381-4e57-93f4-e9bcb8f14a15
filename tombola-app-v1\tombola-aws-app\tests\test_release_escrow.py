from lambda_functions.release_escrow.handler import handler
import json

def test_release_escrow(monkeypatch):
    monkeypatch.setattr("lambda_functions.release_escrow.handler.supabase.table", lambda name: type('', (), {
        "select": lambda self, *a, **kw: type('', (), {"eq": lambda self, k, v: type('', (), {"execute": lambda self: type('', (), {"data": [{"payment_intent_id": "pi_abc"}]})()})()})(),
        "update": lambda self, val: type('', (), {"eq": lambda self, k, v: type('', (), {"execute": lambda self: None})()})(),
    })())

    monkeypatch.setattr("lambda_functions.release_escrow.handler.stripe.PaymentIntent.capture", lambda id: None)
    monkeypatch.setattr("lambda_functions.release_escrow.handler.boto3.client", lambda s: type('', (), {"publish": lambda self, **kw: None})())

    event = {
        "Records": [
            {
                "Sns": {
                    "Message": json.dumps({"product_id": "abc-123"})
                }
            }
        ]
    }
    result = handler(event, {})
    assert result["statusCode"] == 200