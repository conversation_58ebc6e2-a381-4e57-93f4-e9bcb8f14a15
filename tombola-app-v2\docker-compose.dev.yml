

services:
  # Supabase Database (PostgreSQL)
  supabase-db:
    image: supabase/postgres:latest
    container_name: tombola-supabase-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST_AUTH_METHOD: trust
      POSTGRES_PORT: 5432
    ports:
      - "5432:5432"
    volumes:
      - supabase_db_data:/var/lib/postgresql/data
      - ./supabase/migrations:/docker-entrypoint-initdb.d
    networks:
      - tombola-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Supabase Studio (GUI)
  supabase-studio:
    image: supabase/studio:latest
    container_name: tombola-supabase-studio
    restart: unless-stopped
    environment:
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_REST_URL: http://localhost:8000/rest/v1/
      SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SUPABASE_SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      STUDIO_PG_META_URL: http://supabase-meta:8080
      POSTGRES_PASSWORD: postgres
    ports:
      - "3010:3000"
    depends_on:
      supabase-db:
        condition: service_healthy
    networks:
      - tombola-network

  # Supabase Kong (API Gateway)
  supabase-kong:
    image: kong:2.8.1
    container_name: tombola-supabase-kong
    restart: unless-stopped
    environment:
      KONG_DATABASE: "off"
      KONG_DECLARATIVE_CONFIG: /var/lib/kong/kong.yml
      KONG_DNS_ORDER: LAST,A,CNAME
      KONG_PLUGINS: request-transformer,cors,key-auth,acl,basic-auth
    ports:
      - "8000:8000"
    volumes:
      - ./supabase/config/kong.yml:/var/lib/kong/kong.yml:ro
    depends_on:
      - supabase-db
    networks:
      - tombola-network

  # Supabase Meta (Database management)
  supabase-meta:
    image: supabase/postgres-meta:v0.68.0
    container_name: tombola-supabase-meta
    restart: unless-stopped
    environment:
      PG_META_PORT: 8080
      PG_META_DB_HOST: supabase-db
      PG_META_DB_PORT: 5432
      PG_META_DB_NAME: postgres
      PG_META_DB_USER: supabase_admin
      PG_META_DB_PASSWORD: postgres
    depends_on:
      supabase-db:
        condition: service_healthy
    networks:
      - tombola-network

  # Fastify API Gateway
  api:
    build:
      context: ./api-layer
      dockerfile: Dockerfile
      target: development
    container_name: tombola-api
    restart: unless-stopped
    environment:
      NODE_ENV: development
      PORT: 3001
      DATABASE_URL: ***********************************************/postgres
      SUPABASE_URL: http://supabase-kong:8000
      SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0
      SUPABASE_SERVICE_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
      JWT_SECRET: super-secret-jwt-token-with-at-least-32-characters-long
      CORS_ORIGIN: http://localhost:3000
    ports:
      - "3001:3001"
    depends_on:
      supabase-db:
        condition: service_healthy
    volumes:
      - ./api-layer:/app
      - /app/node_modules
    networks:
      - tombola-network

  # Web Frontend (React Native Web)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: tombola-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      EXPO_PUBLIC_API_URL: http://localhost:3001
      EXPO_DEVTOOLS_LISTEN_ADDRESS: 0.0.0.0
      EXPO_CLI_NO_INSTALL_WARNING: 1
    ports:
      - "3000:3000"
    depends_on:
      - api
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - tombola-network
    stdin_open: true
    tty: true

volumes:
  supabase_db_data:
    driver: local

networks:
  tombola-network:
    driver: bridge
