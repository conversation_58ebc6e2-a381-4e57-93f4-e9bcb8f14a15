{"extends": "expo/tsconfig.base", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/components/*": ["components/*"], "@/screens/*": ["screens/*"], "@/services/*": ["services/*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/hooks/*": ["hooks/*"], "@/navigation/*": ["navigation/*"]}}, "include": ["src/**/*", "App.tsx", "app.config.ts"], "exclude": ["node_modules", "dist", "web-build", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"]}