import "../globals.css";
import { Sidebar } from "@/components/Sidebar";
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

export default async function DashboardLayout({
                                                  children,
                                              }: {
    children: React.ReactNode;
}) {
    const cookieStore = await cookies()

    const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_KEY!,
        {
            cookies: {
                get(name: string) {
                    return cookieStore.get(name)?.value
                },
                set(name: string, value: string, options: CookieOptions) {
                    cookieStore.set({ name, value, ...options })
                },
                remove(name: string, options: CookieOptions) {
                    cookieStore.set({ name, value: '', ...options })
                },
            },
        }
    )

    const { data: { session } } = await supabase.auth.getSession();

    if (!session) {
        redirect('/register');
    }

    const { data: profile } = await supabase
        .from('user')
        .select('profile_picture')
        .eq('id', session.user?.id)
        .single();

    let profileImageUrl = null;

    if (profile?.profile_picture) {
        const data = await supabase.storage
            .from('profile')
            .createSignedUrl(profile.profile_picture, 3600);

        profileImageUrl = data?.data?.signedUrl;
    }

    const userProfile: any = {
        profile_picture: profileImageUrl
    };

    return (
        <div className="flex h-screen">
            <Sidebar userProfile={userProfile} />
            <main className="flex-1 flex flex-col gap-6 px-4 flex-1 sm:ml-20 md:ml-64">
                {children}
            </main>
        </div>
    );
}