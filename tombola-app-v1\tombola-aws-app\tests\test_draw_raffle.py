from lambda_functions.draw_raffle.handler import handler

def test_draw_raffle(monkeypatch):
    monkeypatch.setattr("lambda_functions.draw_raffle.handler.supabase.table", lambda name: type('', (), {
        "select": lambda self, *a, **kw: type('', (), {"eq": lambda self, k, v: type('', (), {"execute": lambda self: type('', (), {"data": [{"tickets_sold": 1, "ticket_count": 1, "raffle_drawn": False, "id": "abc-123", "product_name": "Test"}]})()})()})(),
        "update": lambda self, data: type('', (), {"eq": lambda self, k, v: type('', (), {"execute": lambda self: type('', (), {"data": ["ok"]})()})()})(),
    })())

    monkeypatch.setattr("lambda_functions.draw_raffle.handler.random.choice", lambda x: {"user_id": "u1"})
    monkeypatch.setattr("lambda_functions.draw_raffle.handler.boto3.client", lambda s: type('', (), {"publish": lambda self, **kw: None})())

    result = handler({"product_id": "abc-123"}, {})
    assert result["statusCode"] == 200