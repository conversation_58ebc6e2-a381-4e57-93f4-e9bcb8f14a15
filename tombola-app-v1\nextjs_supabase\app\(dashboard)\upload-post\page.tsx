'use client'

import { ProductForm } from "@/components/ProductForm"
import { createProduct } from "./actions"
import { useState } from "react"

export default function UploadPostPage() {
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (data: ProductSubmitData) => {
    const result = await createProduct(data)
    if (result.error) {
      setError(result.error)
    }
  }

  return (
    <div className="max-w-4xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Create New Raffle</h1>
      <ProductForm onSubmit={handleSubmit} error={error} />
    </div>
  )
}

// Add this interface if not already imported
interface ProductSubmitData {
  productName: string;
  description: string;
  price: number;
  ticketCost: number;
  ticketCount: number;
  imageUrls: string[];
}
