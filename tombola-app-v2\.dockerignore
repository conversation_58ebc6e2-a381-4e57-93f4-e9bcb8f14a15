# Tombola V2 Docker Ignore File
# Files and directories to exclude from Docker build context

# Version control
.git
.gitignore
.gitattributes

# Documentation
README*.md
CHANGELOG.md
LICENSE
docs/

# Environment files
.env
.env.*
!.env.docker

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
.next/
.expo/
.expo-shared/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# Docker files
Dockerfile*
docker-compose*.yml
.dockerignore

# Scripts
scripts/

# Database files
*.sql
*.db
*.sqlite

# Uploads and media
uploads/
media/

# Cache
.cache/
.parcel-cache/

# Testing
.jest/
coverage/

# Misc
*.tgz
*.tar.gz
