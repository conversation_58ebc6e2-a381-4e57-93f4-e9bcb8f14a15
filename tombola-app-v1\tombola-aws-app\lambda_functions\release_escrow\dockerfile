# Build stage
FROM python:3.13-slim as build-image

ARG FUNCTION_DIR="/function"
WORKDIR ${FUNCTION_DIR}

# Install system packages
RUN apt-get update && apt-get install -y \
    g++ make cmake unzip libcurl4-openssl-dev && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Copy function code
COPY . ${FUNCTION_DIR}

# 🔁 Copy shared folder from parent directory
COPY lambda_functions/shared ${FUNCTION_DIR}/shared

# Install dependencies into function dir
RUN pip3 install --target ${FUNCTION_DIR} \
    awslambdaric \
    supabase \
    stripe \
    boto3

# Runtime image
FROM python:3.13-slim

ARG FUNCTION_DIR="/function"
WORKDIR ${FUNCTION_DIR}

COPY --from=build-image ${FUNCTION_DIR} ${FUNCTION_DIR}

# Lambda runtime
ENTRYPOINT [ "/usr/local/bin/python3", "-m", "awslambdaric" ]

CMD [ "handler.handler" ]
