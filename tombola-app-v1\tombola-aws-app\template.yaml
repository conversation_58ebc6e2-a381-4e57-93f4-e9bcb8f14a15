AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  Tombola raffle backend using container-based Lambdas

Globals:
  Function:
    Timeout: 30

Resources:

  # SNS Topics
  WinnerSelectedTopic:
    Type: AWS::SNS::Topic
  UserConfirmedDeliveryTopic:
    Type: AWS::SNS::Topic
  EscrowReleasedTopic:
    Type: AWS::SNS::Topic

  # Lambda Functions

  BuyTicketFunction:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageUri: !Sub "${AWS::AccountId}.dkr.ecr.${AWS::Region}.amazonaws.com/tombola-functions/buy-ticket:latest"
      Events:
        Api:
          Type: Api
          Properties:
            Path: /buy-ticket
            Method: POST

  DrawRaffleFunction:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageUri: !Sub "${AWS::AccountId}.dkr.ecr.${AWS::Region}.amazonaws.com/tombola-functions/draw-raffle:latest"
      Events:
        Api:
          Type: Api
          Properties:
            Path: /draw-raffle
            Method: POST

  ConfirmDeliveryFunction:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageUri: !Sub "${AWS::AccountId}.dkr.ecr.${AWS::Region}.amazonaws.com/tombola-functions/confirm-delivery:latest"
      Events:
        Api:
          Type: Api
          Properties:
            Path: /confirm-delivery
            Method: POST

  ReleaseEscrowFunction:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageUri: !Sub "${AWS::AccountId}.dkr.ecr.${AWS::Region}.amazonaws.com/tombola-functions/release-escrow:latest"
      Events:
        EscrowEvent:
          Type: SNS
          Properties:
            Topic: !Ref UserConfirmedDeliveryTopic

  NotificationsFunction:
    Type: AWS::Serverless::Function
    Properties:
      PackageType: Image
      ImageUri: !Sub "${AWS::AccountId}.dkr.ecr.${AWS::Region}.amazonaws.com/tombola-functions/notifications:latest"
      Events:
        WinnerSelected:
          Type: SNS
          Properties:
            Topic: !Ref WinnerSelectedTopic
        EscrowReleased:
          Type: SNS
          Properties:
            Topic: !Ref EscrowReleasedTopic
