import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native';

// API Configuration
const API_BASE_URL = Platform.select({
  web: process.env.EXPO_PUBLIC_API_URL || 'http://localhost:3000',
  default: process.env.EXPO_PUBLIC_API_URL || 'http://********:3000', // Android emulator
});

const API_VERSION = '/api/v1';

// Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  error?: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  expires_in: number;
}

export interface User {
  id: string;
  email: string;
  full_name?: string;
  phone?: string;
  is_verified: boolean;
  created_at: string;
}

export interface Product {
  id: string;
  name: string;
  description: string;
  image_url?: string;
  retail_price: number;
  ticket_price: number;
  max_tickets: number;
  tickets_sold: number;
  status: 'draft' | 'active' | 'sold_out' | 'drawn' | 'completed';
  draw_date?: string;
}

export interface Ticket {
  id: string;
  ticket_number: number;
  purchase_date: string;
  is_winner: boolean;
  product: {
    id: string;
    name: string;
    image_url?: string;
    status: string;
  };
}

class ApiService {
  private client: AxiosInstance;
  private accessToken: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: `${API_BASE_URL}${API_VERSION}`,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
    this.loadStoredToken();
  }

  private setupInterceptors(): void {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        if (this.accessToken) {
          config.headers.Authorization = `Bearer ${this.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          await this.clearTokens();
          // You might want to redirect to login here
        }
        return Promise.reject(error);
      }
    );
  }

  private async loadStoredToken(): Promise<void> {
    try {
      const token = await AsyncStorage.getItem('access_token');
      if (token) {
        this.accessToken = token;
      }
    } catch (error) {
      console.error('Error loading stored token:', error);
    }
  }

  public setAccessToken(token: string): void {
    this.accessToken = token;
  }

  public async clearTokens(): Promise<void> {
    this.accessToken = null;
    try {
      await AsyncStorage.multiRemove(['access_token', 'refresh_token']);
    } catch (error) {
      console.error('Error clearing tokens:', error);
    }
  }

  // Auth endpoints
  public async signUp(email: string, password: string, fullName?: string, phone?: string): Promise<ApiResponse<{ user: User }>> {
    const response = await this.client.post('/auth/signup', {
      email,
      password,
      fullName,
      phone,
    });
    return response.data;
  }

  public async signIn(email: string, password: string): Promise<ApiResponse<{ user: User; session: AuthTokens }>> {
    const response = await this.client.post('/auth/signin', {
      email,
      password,
    });

    const { session } = response.data;
    if (session?.access_token) {
      this.setAccessToken(session.access_token);
      await AsyncStorage.setItem('access_token', session.access_token);
      await AsyncStorage.setItem('refresh_token', session.refresh_token);
    }

    return response.data;
  }

  public async signOut(): Promise<ApiResponse> {
    const response = await this.client.post('/auth/signout');
    await this.clearTokens();
    return response.data;
  }

  public async resetPassword(email: string): Promise<ApiResponse> {
    const response = await this.client.post('/auth/reset-password', { email });
    return response.data;
  }

  public async getCurrentUser(): Promise<ApiResponse<{ user: User }>> {
    const response = await this.client.get('/auth/me');
    return response.data;
  }

  public async updateProfile(updates: Partial<User>): Promise<ApiResponse<{ user: User }>> {
    const response = await this.client.patch('/auth/me', updates);
    return response.data;
  }

  // Product endpoints
  public async getProducts(filters?: {
    category?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<ApiResponse<{ products: Product[]; total: number }>> {
    const response = await this.client.get('/products', { params: filters });
    return response.data;
  }

  public async getProduct(id: string): Promise<ApiResponse<{ product: Product }>> {
    const response = await this.client.get(`/products/${id}`);
    return response.data;
  }

  // Ticket endpoints
  public async getUserTickets(): Promise<ApiResponse<{ tickets: Ticket[] }>> {
    const response = await this.client.get('/tickets/my-tickets');
    return response.data;
  }

  public async purchaseTickets(productId: string, quantity: number, paymentMethodId: string): Promise<ApiResponse<{
    tickets: Array<{ id: string; ticket_number: number }>;
    payment_intent_id: string;
  }>> {
    const response = await this.client.post('/tickets/purchase', {
      product_id: productId,
      quantity,
      payment_method_id: paymentMethodId,
    });
    return response.data;
  }

  // Payment endpoints
  public async createPaymentIntent(productId: string, quantity: number): Promise<ApiResponse<{
    client_secret: string;
    payment_intent_id: string;
    amount: number;
  }>> {
    const response = await this.client.post('/payments/create-intent', {
      product_id: productId,
      quantity,
    });
    return response.data;
  }

  // Health check
  public async healthCheck(): Promise<{ status: string; timestamp: string }> {
    const response = await this.client.get('/health');
    return response.data;
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
