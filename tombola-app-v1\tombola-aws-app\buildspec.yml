version: 0.2

phases:
  install:
    runtime-versions:
      python: 3.11
    commands:
      - cd tombola-aws-app
      - echo "Now in $(pwd)"
      - ls -la
      - echo Logging into Amazon ECR...
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com

  build:
    commands:
      - echo Building and pushing Docker images...

      - docker build -t $ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/buy-ticket:latest -f lambda_functions/buy_ticket/dockerfile .
      - docker build -t $ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/draw-raffle:latest -f lambda_functions/draw_raffle/dockerfile .
      - docker build -t $ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/confirm-delivery:latest -f lambda_functions/confirm_delivery/dockerfile .
      - docker build -t $ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/release-escrow:latest -f lambda_functions/release_escrow/dockerfile .
      - docker build -t $ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/notifications:latest -f lambda_functions/notifications/dockerfile .

      - docker push $ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/buy-ticket:latest
      - docker push $ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/draw-raffle:latest
      - docker push $ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/confirm-delivery:latest
      - docker push $ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/release-escrow:latest
      - docker push $ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/notifications:latest

      - echo Running SAM deploy...
      - sam deploy --template-file template.yaml --no-confirm-changeset --no-fail-on-empty-changeset --stack-name tombola-stack --capabilities CAPABILITY_IAM --region $AWS_DEFAULT_REGION --image-repositories "BuyTicketFunction=$ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/buy-ticket" "DrawRaffleFunction=$ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/draw-raffle" "ConfirmDeliveryFunction=$ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/confirm-delivery" "ReleaseEscrowFunction=$ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/release-escrow" "NotificationsFunction=$ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/tombola-functions/notifications"

artifacts:
  files:
    - template.yaml
