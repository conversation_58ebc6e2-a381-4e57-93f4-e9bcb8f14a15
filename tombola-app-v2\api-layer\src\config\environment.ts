import { config as dotenvConfig } from 'dotenv';

// Load environment variables
dotenvConfig();

interface Config {
  env: string;
  port: number;
  host: string;
  
  // Database (Supabase)
  supabase: {
    url: string;
    anonKey: string;
    serviceRoleKey: string;
  };
  
  // JWT Configuration
  jwt: {
    secret: string;
    expiresIn: string;
  };
  
  // AWS Configuration
  aws: {
    region: string;
    accessKeyId: string;
    secretAccessKey: string;
    lambda: {
      buyTicket: string;
      drawRaffle: string;
      confirmDelivery: string;
      releaseEscrow: string;
      notifications: string;
    };
    sns: {
      winnerSelected: string;
      userConfirmedDelivery: string;
      escrowReleased: string;
    };
  };
  
  // Stripe Configuration
  stripe: {
    secretKey: string;
    publishableKey: string;
    webhookSecret: string;
  };
  
  // CORS Configuration
  cors: {
    origins: string[];
  };
  
  // Rate Limiting
  rateLimit: {
    max: number;
    timeWindow: string;
  };
  
  // Logging
  logging: {
    level: string;
  };
}

const requiredEnvVars = [
  'SUPABASE_URL',
  'SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'JWT_SECRET',
  'AWS_REGION',
  'AWS_ACCESS_KEY_ID',
  'AWS_SECRET_ACCESS_KEY',
  'STRIPE_SECRET_KEY',
];

// Validate required environment variables
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

export const config: Config = {
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT || '3000', 10),
  host: process.env.HOST || '0.0.0.0',
  
  supabase: {
    url: process.env.SUPABASE_URL!,
    anonKey: process.env.SUPABASE_ANON_KEY!,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  },
  
  jwt: {
    secret: process.env.JWT_SECRET!,
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
  
  aws: {
    region: process.env.AWS_REGION!,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
    lambda: {
      buyTicket: process.env.AWS_LAMBDA_BUY_TICKET || 'tombola-BuyTicketFunction',
      drawRaffle: process.env.AWS_LAMBDA_DRAW_RAFFLE || 'tombola-DrawRaffleFunction',
      confirmDelivery: process.env.AWS_LAMBDA_CONFIRM_DELIVERY || 'tombola-ConfirmDeliveryFunction',
      releaseEscrow: process.env.AWS_LAMBDA_RELEASE_ESCROW || 'tombola-ReleaseEscrowFunction',
      notifications: process.env.AWS_LAMBDA_NOTIFICATIONS || 'tombola-NotificationsFunction',
    },
    sns: {
      winnerSelected: process.env.AWS_SNS_WINNER_SELECTED || '',
      userConfirmedDelivery: process.env.AWS_SNS_USER_CONFIRMED_DELIVERY || '',
      escrowReleased: process.env.AWS_SNS_ESCROW_RELEASED || '',
    },
  },
  
  stripe: {
    secretKey: process.env.STRIPE_SECRET_KEY!,
    publishableKey: process.env.STRIPE_PUBLISHABLE_KEY || '',
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET || '',
  },
  
  cors: {
    origins: process.env.CORS_ORIGINS 
      ? process.env.CORS_ORIGINS.split(',')
      : ['http://localhost:3000', 'http://localhost:19006'], // React Native web dev server
  },
  
  rateLimit: {
    max: parseInt(process.env.RATE_LIMIT_MAX || '100', 10),
    timeWindow: process.env.RATE_LIMIT_WINDOW || '15 minutes',
  },
  
  logging: {
    level: process.env.LOG_LEVEL || 'info',
  },
};

// Validate configuration
if (config.port < 1 || config.port > 65535) {
  throw new Error('PORT must be between 1 and 65535');
}

if (config.rateLimit.max < 1) {
  throw new Error('RATE_LIMIT_MAX must be greater than 0');
}

export default config;
