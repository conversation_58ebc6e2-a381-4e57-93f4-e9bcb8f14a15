'use server'

import { Database } from '@/types/supabase';
import { createClient } from '@/utils/supabase/server'
import { revalidatePath } from 'next/cache'

type ProductInsert = Database['public']['Tables']['product']['Insert']

interface ProductSubmitData {
  productName: string;
  description: string;
  price: number;
  ticketCost: number;
  ticketCount: number;
  imageUrls: string[];
}

export async function createProduct(data: ProductSubmitData) {
  const supabase = await createClient()

  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) throw new Error('Not authenticated')

    const productData: ProductInsert = {
      product_name: data.productName,
      description: data.description,
      price: data.price,
      ticket_cost: data.ticketCost,
      ticket_count: data.ticketCount,
      user_id: user.id,
      product_delivered: false,
      product_sent: false,
      raffle_drawn: false,
      product_images: data.imageUrls
    }

    const { data: product, error } = await supabase
      .from('product')
      .insert(productData)
      .select()
      .single()

    if (error) throw error

    revalidatePath('/dashboard')
    return { data: product, error: null }

  } catch (error) {
    return { 
      data: null, 
      error: error instanceof Error ? error.message : 'An error occurred' 
    }
  }
}

export async function createStripeConnectAccount() {
  const supabase = await createClient()

  try {
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) throw new Error('Not authenticated')

    // Check if user already has a Stripe account ID
    const { data: userData, error: userError } = await supabase
      .from('user')
      .select('stripe_connect_account_id')
      .eq('id', user.id)
      .single()

    if (userError) throw userError

    if (userData?.stripe_connect_account_id) {
      return { account: userData.stripe_connect_account_id, error: null }
    }

    // Create Stripe Connect Account
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/stripe/account`, {
      method: "POST",
    })

    const { account } = await response.json()
    
    if (!account) throw new Error('Failed to create Stripe account')

    // Update user profile with Stripe account ID
    const { error: updateError } = await supabase
      .from('user')
      .update({ stripe_connect_account_id: account })
      .eq('id', user.id)

    if (updateError) throw updateError

    return { account, error: null }

  } catch (error) {
    return {
      account: null,
      error: error instanceof Error ? error.message : 'An error occurred'
    }
  }
}
