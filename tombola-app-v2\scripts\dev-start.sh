#!/bin/bash

# Tombola V2 Development Startup Script
# This script starts the web-first development environment with Docker Compose

set -e

echo "🚀 Starting Tombola V2 Development Environment (Web-first)..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if .env file exists, if not copy from .env.docker
if [ ! -f .env ]; then
    echo "📋 Creating .env file from .env.docker template..."
    cp .env.docker .env
    echo "⚠️  Please update the .env file with your actual configuration values."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p database/init
mkdir -p nginx/ssl
mkdir -p api-layer/uploads
mkdir -p frontend/dist

# Build and start services using development compose file
echo "🔨 Building and starting Docker services..."
docker-compose -f docker-compose.dev.yml up --build -d

# Wait for Supabase database to be ready
echo "⏳ Waiting for Supabase database to be ready..."
timeout=60
counter=0
while ! docker-compose -f docker-compose.dev.yml exec -T supabase-db pg_isready -U postgres -d postgres > /dev/null 2>&1; do
    if [ $counter -eq $timeout ]; then
        echo "❌ Supabase database failed to start within $timeout seconds"
        docker-compose -f docker-compose.dev.yml logs supabase-db
        exit 1
    fi
    echo "Waiting for Supabase database... ($counter/$timeout)"
    sleep 1
    counter=$((counter + 1))
done

echo "✅ Supabase database is ready!"

# Wait for Supabase Kong API Gateway to be ready
echo "⏳ Waiting for Supabase API Gateway to be ready..."
timeout=60
counter=0
while ! curl -f http://localhost:8000/health > /dev/null 2>&1; do
    if [ $counter -eq $timeout ]; then
        echo "❌ Supabase API Gateway failed to start within $timeout seconds"
        docker-compose -f docker-compose.dev.yml logs supabase-kong
        exit 1
    fi
    echo "Waiting for Supabase API Gateway... ($counter/$timeout)"
    sleep 1
    counter=$((counter + 1))
done

echo "✅ Supabase API Gateway is ready!"

# Wait for API to be ready
echo "⏳ Waiting for API to be ready..."
timeout=60
counter=0
while ! curl -f http://localhost:3001/health > /dev/null 2>&1; do
    if [ $counter -eq $timeout ]; then
        echo "❌ API failed to start within $timeout seconds"
        docker-compose -f docker-compose.dev.yml logs api
        exit 1
    fi
    echo "Waiting for API... ($counter/$timeout)"
    sleep 1
    counter=$((counter + 1))
done

echo "✅ API is ready!"

# Show service status
echo "📊 Service Status:"
docker-compose -f docker-compose.dev.yml ps

echo ""
echo "🎉 Tombola V2 Web-first Development Environment is ready!"
echo ""
echo "🌐 Frontend (Web): http://localhost:3000"
echo "🔧 API (Fastify): http://localhost:3001"
echo "🗄️  Supabase Database: localhost:5432"
echo "🎛️  Supabase Studio (GUI): http://localhost:3010"
echo "🌐 Supabase API Gateway: http://localhost:8000"
echo ""
echo "📋 Useful commands:"
echo "  View logs: docker-compose -f docker-compose.dev.yml logs -f [service_name]"
echo "  Stop services: docker-compose -f docker-compose.dev.yml down"
echo "  Restart service: docker-compose -f docker-compose.dev.yml restart [service_name]"
echo "  Database shell: docker-compose -f docker-compose.dev.yml exec supabase-db psql -U postgres -d postgres"
echo "  API shell: docker-compose -f docker-compose.dev.yml exec api sh"
echo "  Frontend shell: docker-compose -f docker-compose.dev.yml exec frontend sh"
echo ""
echo "🔑 Supabase Keys (for development):"
echo "  Anon Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0"
echo "  Service Key: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU"
echo ""
echo "🔧 To stop the environment, run: ./scripts/dev-stop.sh"
