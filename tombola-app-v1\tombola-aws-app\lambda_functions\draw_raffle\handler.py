def handler(event, context):
    from shared.supabase_client import supabase
    from shared.logger import log
    import random
    import boto3

    sns = boto3.client("sns")

    try:
        product_id = event["product_id"]
        product = supabase.table("product").select("*").eq("id", product_id).execute().data[0]
        if product["tickets_sold"] < product["ticket_count"] or product["raffle_drawn"]:
            return {"statusCode": 400, "message": "Raffle cannot be drawn yet"}

        tickets = supabase.table("ticket").select("*").eq("product_id", product_id).execute().data
        winner = random.choice(tickets)["user_id"]

        supabase.table("product").update({
            "raffle_drawn": True,
            "winner_id": winner,
            "status": "awaiting_confirmation"
        }).eq("id", product_id).execute()

        sns.publish(
            TopicArn="<WinnerSelectedTopic>",
            Message=f"Winner selected: {winner} for product {product_id}"
        )
        log("Raffle drawn", {"product_id": product_id, "winner": winner})
        return {"statusCode": 200, "winner": winner}
    except Exception as e:
        log("Error in draw_raffle", str(e))
        return {"statusCode": 500, "error": str(e)}

