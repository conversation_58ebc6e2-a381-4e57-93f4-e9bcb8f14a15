# Tombola Raffle Application

This repository contains both versions of the Tombola Raffle application:

## 📁 Project Structure

```
tombola20/
├── README.md                    # This file
├── tombola-app-v1/             # Original application (Supabase + Next.js)
│   ├── nextjs_supabase/        # Next.js frontend with Supabase
│   ├── tombola-aws-app/        # AWS Lambda backend
│   ├── supabase/               # Supabase configuration
│   └── README.md               # V1 documentation
│
└── tombola-app-v2/             # New application (Appwrite + React Native)
    ├── aws-backend/            # Updated AWS Lambda functions
    ├── api-layer/              # Node.js Express API gateway
    ├── frontend/               # React Native (web-first)
    ├── backend/                # Additional backend services
    ├── project-structure.md    # V2 architecture documentation
    ├── implementation-plan.md  # Implementation roadmap
    └── REWRITE_SUMMARY.md      # Migration summary
```

## 🔄 Version Comparison

### Version 1 (Original)
- **Frontend**: Next.js with React
- **Database**: Supabase (PostgreSQL)
- **Backend**: AWS Lambda functions
- **Auth**: Supabase Auth
- **Payments**: Stripe
- **Status**: Production-ready, stable

### Version 2 (New Architecture)
- **Frontend**: React Native (web-first, mobile-ready)
- **Database**: Self-hosted Supabase
- **Backend**: AWS Lambda functions (kept) + Node.js API layer (new)
- **Auth**: Supabase Auth
- **Payments**: Stripe (kept)
- **Status**: In development

## 🚀 Getting Started

### For Version 1 (Current Production)
```bash
cd tombola-app-v1/nextjs_supabase
npm install
npm run dev
```

### For Version 2 (New Development)
```bash
# Start Appwrite (database)
cd tombola-app-v2
# Follow setup instructions in implementation-plan.md

# Start API layer
cd api-layer
npm install
npm run dev

# Start React Native frontend
cd frontend
npm install
npm run web
```

## 📚 Documentation

- **V1 Documentation**: See `tombola-app-v1/README.md`
- **V2 Architecture**: See `tombola-app-v2/project-structure.md`
- **Migration Plan**: See `tombola-app-v2/implementation-plan.md`
- **Migration Summary**: See `tombola-app-v2/REWRITE_SUMMARY.md`

## 🎯 Migration Strategy

The migration from V1 to V2 is designed to:
1. **Preserve** your existing AWS Lambda business logic
2. **Replace** Supabase with self-hosted Appwrite
3. **Add** a Node.js API layer for better frontend integration
4. **Modernize** the frontend with React Native (web-first)
5. **Enable** future mobile app development

## 🔧 Development Workflow

1. **Current Development**: Continue using V1 for production
2. **New Features**: Develop in V2 architecture
3. **Testing**: Parallel testing of both versions
4. **Migration**: Gradual migration when V2 is stable
5. **Deployment**: Switch to V2 when ready

## 📞 Support

For questions about:
- **V1 (Current)**: Check existing documentation in `tombola-app-v1/`
- **V2 (New)**: Check migration documentation in `tombola-app-v2/`
- **Migration Process**: See `tombola-app-v2/REWRITE_SUMMARY.md`
