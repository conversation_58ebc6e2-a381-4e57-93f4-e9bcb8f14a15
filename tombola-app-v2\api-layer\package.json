{"name": "tombola-api-layer", "version": "1.0.0", "description": "Fastify TypeScript API for Tombola Raffle App - web-first with mobile support", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "tsx watch src/server.ts", "test": "jest", "lint": "eslint src/ --ext .ts", "lint:fix": "eslint src/ --ext .ts --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@fastify/cors": "^8.4.0", "@fastify/helmet": "^11.1.1", "@fastify/jwt": "^9.1.0", "@fastify/swagger": "^8.12.0", "@fastify/swagger-ui": "^2.1.0", "@supabase/supabase-js": "^2.38.4", "dotenv": "^16.3.1", "fastify": "^4.24.3", "fastify-type-provider-zod": "^1.1.9", "pino": "^8.16.1", "pino-pretty": "^10.2.3", "uuid": "^9.0.0", "zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.6", "@types/node": "^20.8.7", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "eslint": "^8.52.0", "eslint-config-prettier": "^9.0.0", "jest": "^29.7.0", "prettier": "^3.0.3", "ts-jest": "^29.1.1", "tsx": "^4.20.3", "typescript": "^5.2.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["api", "fastify", "typescript", "tombola", "raffle", "supabase", "web", "react-native"], "author": "Tombola Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/tombola-app-v2.git"}}