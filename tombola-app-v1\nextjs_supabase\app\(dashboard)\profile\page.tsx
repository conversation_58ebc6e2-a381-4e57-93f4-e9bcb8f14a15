import { Metada<PERSON> } from "next"
import EditProfileForm from "@/views/profile/profile-view"
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

export const metadata: Metadata = {
  title: "Edit Profile | Your App",
  description: "Edit your profile settings",
}

export default async function ProfilePage() {
  const cookieStore = await cookies()
  const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            cookieStore.set({ name, value, ...options })
          },
          remove(name: string, options: any) {
            cookieStore.set({ name, value: '', ...options })
          },
        },
      }
  )

  const { data: { user } } = await supabase.auth.getUser()
  if (!user) redirect('/login')

  const { data: profile } = await supabase
      .from('user')
      .select('username, bio, profile_picture')
      .eq('id', user.id)
      .single()

  if (!profile) redirect('/login')

  // Get follower count (people following this user)
  const { data: followers } = await supabase
      .from('follow')
      .select('follower_id')
      .eq('followee_id', user.id)

  // Get following count (people this user follows)
  const { data: following } = await supabase
      .from('follow')
      .select('followee_id')
      .eq('follower_id', user.id)

  let profileImageUrl = null
  if (profile.profile_picture) {
    const { data } = await supabase.storage
        .from('profile')
        .createSignedUrl(profile.profile_picture, 3600)

    profileImageUrl = data?.signedUrl
  }

  const initialData: any = {
    ...profile,
    profile_picture: profileImageUrl,
    followersCount: followers?.length || 0,
    followingCount: following?.length || 0
  }

  return <EditProfileForm initialData={initialData} />
}
