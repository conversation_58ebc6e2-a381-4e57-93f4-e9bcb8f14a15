'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2 } from 'lucide-react'
import { createClient } from "@/utils/supabase/client"
import * as z from 'zod'
import { ForgotPasswordSchema } from '@/schemas/forgot-schema'

type FormData = z.infer<typeof ForgotPasswordSchema>

export default function ForgotPasswordForm() {
    const [isLoading, setIsLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)
    const [success, setSuccess] = useState(false)
    const router = useRouter()
    const supabase = createClient()

    const { register, handleSubmit, formState: { errors } } = useForm<FormData>({
        resolver: zodResolver(ForgotPasswordSchema)
    })

    const onSubmit = async (data: FormData) => {
        setIsLoading(true)
        setError(null)
        setSuccess(false)

        try {
            const { error } = await supabase.auth.resetPasswordForEmail(data.email, {
                redirectTo: `${window.location.origin}/reset-password`,
            })

            if (error) throw error

            setSuccess(true)
        } catch (err: any) {
            setError(err?.message || 'Failed to send reset email')
        } finally {
            setIsLoading(false)
        }
    }

    return (
        <div className='min-h-screen bg-gray-100 flex items-center justify-center flex-col'>
            <div className="flex w-full max-w-md space-y-8 justify-center mt-5 ">
                <div>
                    <h1 className="text-2xl font-bold text-primary text-center">
                        <span className="hidden md:block">Tombola</span>
                        <span className="md:hidden">T</span>
                    </h1>
                </div>

            </div>

            <div className="flex p-4">


                <div className="w-full max-w-md space-y-8">
                    <div>
                        <h2 className="text-center text-3xl font-bold tracking-tight text-gray-900">
                            Reset your password
                        </h2>
                        <p className="mt-2 text-center text-sm text-gray-600">
                            Enter your email address and we'll send you a link to reset your password
                        </p>
                    </div>

                    <Card>
                        <CardContent className="pt-6">
                            {error && (
                                <Alert variant="destructive" className="mb-6">
                                    <AlertCircle className="h-4 w-4"/>
                                    <AlertDescription>{error}</AlertDescription>
                                </Alert>
                            )}

                            {success && (
                                <Alert className="mb-6 bg-green-50 border-green-200">
                                    <CheckCircle2 className="h-4 w-4 text-green-600"/>
                                    <AlertDescription className="text-green-700">
                                        Check your email for a link to reset your password
                                    </AlertDescription>
                                </Alert>
                            )}

                            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                                <div>
                                    <Label htmlFor="email">Email address</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        autoComplete="email"
                                        {...register('email')}
                                        className={errors.email ? 'border-red-500' : ''}
                                        disabled={isLoading || success}
                                    />
                                    {errors.email && (
                                        <p className="mt-1 text-sm text-red-500">{errors.email.message}</p>
                                    )}
                                </div>

                                <Button
                                    type="submit"
                                    className="w-full"
                                    disabled={isLoading || success}
                                >
                                    {isLoading ? 'Sending...' : 'Send reset link'}
                                </Button>
                            </form>
                        </CardContent>

                        <div className="p-6 bg-gray-50 rounded-b-lg text-center">
                            <p className="text-sm text-gray-600">
                                Remember your password?{' '}
                                <Link
                                    href="/login"
                                    className="font-semibold text-blue-600 hover:text-blue-500"
                                >
                                    Sign in
                                </Link>
                            </p>
                        </div>
                    </Card>
                </div>
            </div>
        </div>
    )
}
