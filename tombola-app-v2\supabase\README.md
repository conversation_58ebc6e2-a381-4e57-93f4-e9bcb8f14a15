# Tombola V2 Supabase Setup

This directory contains the complete Supabase configuration for the Tombola V2 application, including database schema, migrations, Row Level Security (RLS) policies, and seed data.

## 📁 Directory Structure

```
supabase/
├── config.toml                           # Supabase project configuration
├── migrations/                           # Database migrations
│   ├── 20241225000001_initial_schema.sql # Initial database schema
│   ├── 20241225000002_rls_policies.sql   # Row Level Security policies
│   └── 20241225000003_functions_triggers.sql # Database functions and triggers
├── seed.sql                              # Sample data for development
└── README.md                             # This file
```

## 🚀 Quick Start

### Prerequisites

1. Install Supabase CLI:
   ```bash
   npm install -g supabase
   ```

2. Docker Desktop (for local development)

### Local Development Setup

1. **Initialize Supabase locally:**
   ```bash
   cd tombola-app-v2
   supabase start
   ```

2. **Apply migrations:**
   ```bash
   supabase db reset
   ```

3. **Access local services:**
   - **Supabase Studio:** http://localhost:54323
   - **API Gateway:** http://localhost:54321
   - **Database:** postgresql://postgres:postgres@localhost:54322/postgres

### Production Setup

1. **Create a new Supabase project:**
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Note your project URL and anon key

2. **Link your local project:**
   ```bash
   supabase link --project-ref YOUR_PROJECT_ID
   ```

3. **Push migrations to production:**
   ```bash
   supabase db push
   ```

## 📊 Database Schema

### Core Tables

#### Users (`public.user`)
- Extends Supabase Auth users
- Stores user profiles, roles, and metadata
- Linked to `auth.users` via foreign key

#### Products (`public.product`)
- Raffle products with pricing and ticket information
- Supports image galleries and categorization
- Tracks raffle status and winner information

#### Tickets (`public.ticket`)
- Individual raffle tickets purchased by users
- Auto-assigned ticket numbers
- Tracks winning status

#### Payments (`public.payment`)
- Payment records with Stripe integration
- Supports multiple payment statuses
- Links to purchased tickets

#### Categories & Subcategories
- Hierarchical product categorization
- Admin-managed with public read access

### Supporting Tables

- **Comments:** User comments on products
- **Follows:** User following relationships
- **Notifications:** System notifications
- **Messages:** Direct messaging between users
- **Charity & Charity Membership:** Charity integration

## 🔒 Security Features

### Row Level Security (RLS)

All tables have RLS enabled with comprehensive policies:

- **Public Read:** Categories, subcategories, active products
- **User-Scoped:** Users can only modify their own data
- **Admin-Only:** Category management, charity verification
- **Authenticated-Only:** Payments, notifications, messages

### Data Validation

- **Triggers:** Automatic ticket numbering, sold count updates
- **Constraints:** Unique relationships, data integrity
- **Functions:** Business logic enforcement

## 🔧 Database Functions

### Core Functions

- `handle_new_user()`: Auto-creates user profile on auth signup
- `update_tickets_sold()`: Maintains ticket count accuracy
- `assign_ticket_number()`: Sequential ticket numbering
- `validate_ticket_purchase()`: Prevents overselling
- `complete_product_raffle()`: Automated winner selection
- `create_notification()`: Notification system

### Triggers

- **Updated At:** Automatic timestamp updates
- **User Creation:** Profile creation on auth signup
- **Ticket Management:** Validation and numbering
- **Business Logic:** Automated raffle completion

## 📝 Environment Variables

Required environment variables for the API layer:

```env
# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# Database (for direct connections if needed)
DATABASE_URL=your_database_url
```

## 🧪 Development Data

The `seed.sql` file includes:

- **Sample Categories:** Electronics, Fashion, Home & Garden, etc.
- **Subcategories:** Organized by parent category
- **Verified Charities:** Sample charity organizations
- **Test Data:** Commented out user and product samples

To load seed data:
```bash
supabase db reset  # Includes seed data
```

## 📚 API Integration

The database is designed to work seamlessly with the Fastify API layer:

### TypeScript Types
- Generated types in `api-layer/src/types/database.ts`
- Matches exact database schema
- Supports all custom enums and relationships

### Service Layer
- Dual client setup (user/admin operations)
- RLS-aware queries
- Proper error handling

## 🔄 Migration Management

### Creating New Migrations

```bash
# Create a new migration
supabase migration new migration_name

# Apply migrations locally
supabase db reset

# Push to production
supabase db push
```

### Migration Best Practices

1. **Incremental Changes:** Small, focused migrations
2. **Backwards Compatible:** Avoid breaking changes
3. **Data Safety:** Always backup before major changes
4. **Testing:** Test migrations locally first

## 🛠️ Maintenance

### Regular Tasks

1. **Monitor Performance:** Check slow queries
2. **Update Statistics:** Analyze table usage
3. **Backup Data:** Regular database backups
4. **Security Review:** Audit RLS policies

### Troubleshooting

- **Connection Issues:** Check environment variables
- **Permission Errors:** Verify RLS policies
- **Migration Failures:** Check for conflicts
- **Performance Issues:** Review indexes and queries

## 📖 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [Supabase CLI Reference](https://supabase.com/docs/reference/cli)
