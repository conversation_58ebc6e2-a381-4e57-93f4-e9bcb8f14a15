import { <PERSON>ada<PERSON> } from "next"
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { redirect, notFound } from 'next/navigation'
import { getUserProfile } from "./actions"
import UserProfileView from "@/views/profile/profile-user-view";

type Props = {
    params: { slug: string }
}

export default async function UserProfilePage({ params }: { params: any }) {
    const cookieStore = await cookies()
    const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
            cookies: {
                get(name: string) {
                    return cookieStore.get(name)?.value
                },
                set(name: string, value: string, options: any) {
                    cookieStore.set({ name, value, ...options })
                },
                remove(name: string, options: any) {
                    cookieStore.set({ name, value: '', ...options })
                },
            },
        }
    )

    const profile = await getUserProfile(params.slug, supabase)
    if (!profile) return notFound()

    // Get signed URL for profile picture
    let profileImageUrl = null
    if (profile.profile_picture) {
        const { data } = await supabase.storage
            .from('profile')
            .createSignedUrl(profile.profile_picture, 3600)

        profileImageUrl = data?.signedUrl
    }

    // Get current user to determine if this is their own profile
    const { data: { user } } = await supabase.auth.getUser()

    const profileData: any = {
        ...profile,
        profile_picture: profileImageUrl,
        isOwnProfile: user?.id === profile.id
    }

    return <UserProfileView profile={profileData} />
}
