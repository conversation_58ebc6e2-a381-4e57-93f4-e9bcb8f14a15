import React, { createContext, useContext, ReactNode } from 'react';
import { apiService, Product, Ticket } from '../services/api';

interface ApiContextType {
  // Product methods
  getProducts: (filters?: {
    category?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }) => Promise<{ products: Product[]; total: number }>;
  getProduct: (id: string) => Promise<Product>;
  
  // Ticket methods
  getUserTickets: () => Promise<Ticket[]>;
  purchaseTickets: (
    productId: string,
    quantity: number,
    paymentMethodId: string
  ) => Promise<{
    tickets: Array<{ id: string; ticket_number: number }>;
    payment_intent_id: string;
  }>;
  
  // Payment methods
  createPaymentIntent: (
    productId: string,
    quantity: number
  ) => Promise<{
    client_secret: string;
    payment_intent_id: string;
    amount: number;
  }>;
  
  // Health check
  healthCheck: () => Promise<{ status: string; timestamp: string }>;
}

const ApiContext = createContext<ApiContextType | undefined>(undefined);

interface ApiProviderProps {
  children: ReactNode;
}

export const ApiProvider: React.FC<ApiProviderProps> = ({ children }) => {
  const getProducts = async (filters?: {
    category?: string;
    status?: string;
    limit?: number;
    offset?: number;
  }) => {
    const response = await apiService.getProducts(filters);
    return response;
  };

  const getProduct = async (id: string) => {
    const response = await apiService.getProduct(id);
    return response.product;
  };

  const getUserTickets = async () => {
    const response = await apiService.getUserTickets();
    return response.tickets;
  };

  const purchaseTickets = async (
    productId: string,
    quantity: number,
    paymentMethodId: string
  ) => {
    const response = await apiService.purchaseTickets(productId, quantity, paymentMethodId);
    return response;
  };

  const createPaymentIntent = async (productId: string, quantity: number) => {
    const response = await apiService.createPaymentIntent(productId, quantity);
    return response;
  };

  const healthCheck = async () => {
    return await apiService.healthCheck();
  };

  const value: ApiContextType = {
    getProducts,
    getProduct,
    getUserTickets,
    purchaseTickets,
    createPaymentIntent,
    healthCheck,
  };

  return (
    <ApiContext.Provider value={value}>
      {children}
    </ApiContext.Provider>
  );
};

export const useApi = (): ApiContextType => {
  const context = useContext(ApiContext);
  if (context === undefined) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};
