import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { Type } from '@sinclair/typebox';
import { authMiddleware } from '../middleware/auth';

export default async function userRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
) {
  // Get user profile (protected route)
  fastify.get('/profile', {
    preHandler: [authMiddleware],
    schema: {
      response: {
        200: Type.Object({
          user: Type.Object({
            id: Type.String(),
            email: Type.String(),
            full_name: Type.Union([Type.String(), Type.Null()]),
            phone: Type.Union([Type.String(), Type.Null()]),
          }),
        }),
      },
    },
  }, async (request, reply) => {
    // Implementation will be added later
    reply.send({
      user: request.user,
    });
  });

  // Update user profile
  fastify.patch('/profile', {
    preHandler: [authMiddleware],
    schema: {
      body: Type.Object({
        full_name: Type.Optional(Type.String()),
        phone: Type.Optional(Type.String()),
        address: Type.Optional(Type.String()),
      }),
    },
  }, async (request, reply) => {
    // Implementation will be added later
    reply.send({ message: 'Profile updated successfully' });
  });
}
