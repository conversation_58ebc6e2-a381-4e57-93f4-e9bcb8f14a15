import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatTimestamp(timestamp: Date) {
  const now = new Date();
  const timeDifference = now.getTime() - timestamp.getTime();
  const seconds = Math.floor(timeDifference / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (seconds <= 60) {
    return "A FEW SECONDS AGO";
  } else if (minutes < 60) {
    return `${minutes} MINUTE${minutes > 1 ? 'S' : ''} AGO`;
  }else if (hours < 24) {
    return `${hours} HOUR${hours > 1 ? 'S' : ''} AGO`;
  } else {
    return `${days} DAY${days > 1 ? 'S' : ''} AGO`;
  }
}