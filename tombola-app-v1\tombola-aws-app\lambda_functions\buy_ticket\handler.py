
def handler(event, context):
    from shared.supabase_client import supabase
    from shared.stripe_client import stripe
    from shared.logger import log
    try:
        payment_intent = stripe.PaymentIntent.create(
            amount=int(event["ticket_cost"] * 100),
            currency="aud",
            payment_method=event["payment_method_id"],
            confirm=True,
            confirmation_method="manual",
            capture_method="manual",
            automatic_payment_methods={"enabled": True}
        )
        supabase.table("ticket").insert({
            "product_id": event["product_id"],
            "user_id": event["user_id"],
            "payment_intent_id": payment_intent.id
        }).execute()
        log("Ticket purchased", payment_intent.id)
        return {"statusCode": 200, "payment_intent_id": payment_intent.id}
    except Exception as e:
        log("Error in buy_ticket", str(e))
        return {"statusCode": 500, "error": str(e)}
