import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { SubmitButton } from "@/components/submit-button"
import { useState } from "react"
import { createClient } from "@/utils/supabase/client"

interface ProductFormProps {
  onSubmit: (data: ProductSubmitData) => Promise<void>
  error: string | null
}

interface ProductSubmitData {
  productName: string;
  description: string;
  price: number;
  ticketCost: number;
  ticketCount: number;
  imageUrls: string[];
}

export function ProductForm({ onSubmit, error }: ProductFormProps) {
  const [selectedImages, setSelectedImages] = useState<File[]>([])
  const [previewUrls, setPreviewUrls] = useState<string[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    setSelectedImages(prev => [...prev, ...files])
    
    // Create preview URLs
    files.forEach(file => {
      const reader = new FileReader()
      reader.onloadend = () => {
        setPreviewUrls(prev => [...prev, reader.result as string])
      }
      reader.readAsDataURL(file)
    })
  }

  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index))
    setPreviewUrls(prev => prev.filter((_, i) => i !== index))
  }

  const uploadImages = async (images: File[]) => {
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    const urls = []
    for (const image of images) {
      const fileName = `${user.id}/${Date.now()}-${image.name}`
      const { data, error } = await supabase
        .storage
        .from('product')
        .upload(fileName, image, {
          cacheControl: '3600',
          upsert: false
        })

      if (error) throw error

      const { data: { publicUrl } } = supabase
        .storage
        .from('product')
        .getPublicUrl(fileName)

      urls.push(publicUrl)
    }
    return urls
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsUploading(true);
    try {
      const form = e.currentTarget;
      const imageUrls = await uploadImages(selectedImages);
      
      const formData: ProductSubmitData = {
        productName: (form.productName as HTMLInputElement).value,
        description: (form.description as HTMLTextAreaElement).value,
        price: parseFloat((form.price as HTMLInputElement).value),
        ticketCost: parseFloat((form.ticketCost as HTMLInputElement).value),
        ticketCount: parseInt((form.ticketCount as HTMLInputElement).value),
        imageUrls: imageUrls,
      };
      
      await onSubmit(formData);
      setIsSuccess(true);
    } catch (error) {
      setIsSuccess(false);
    } finally {
      setIsUploading(false);
    }
  }

  if (isSuccess) {
    return (
      <div className="text-center py-8">
        <h2 className="text-2xl font-bold text-green-600 mb-2">Raffle Created Successfully!</h2>
        <p className="text-gray-600 mb-4">Your raffle has been posted and is now live.</p>
        <button
          onClick={() => window.location.href = '/dashboard'}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Return to Dashboard
        </button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="productName">Product Name</Label>
        <Input
          id="productName"
          name="productName"
          required
        />
      </div>
      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          name="description"
          required
        />
      </div>
      <div>
        <Label htmlFor="price">Product Value</Label>
        <Input
          id="price"
          name="price"
          type="number"
          step="0.01"
          min="0"
          required
        />
      </div>
      <div>
        <Label htmlFor="ticketCost">Ticket Price</Label>
        <Input
          id="ticketCost"
          name="ticketCost"
          type="number"
          step="0.01"
          min="0"
          required
        />
      </div>
      <div>
        <Label htmlFor="ticketCount">Number of Tickets</Label>
        <Input
          id="ticketCount"
          name="ticketCount"
          type="number"
          min="1"
          required
        />
      </div>
      
      <div>
        <Label htmlFor="images">Product Images</Label>
        <Input
          id="images"
          name="images"
          type="file"
          accept="image/*"
          multiple
          onChange={handleImageChange}
          className="mb-2"
        />
        {previewUrls.length > 0 && (
          <div className="grid grid-cols-3 gap-2 mt-2">
            {previewUrls.map((url, index) => (
              <div key={index} className="relative">
                <img src={url} alt={`Preview ${index + 1}`} className="w-full h-32 object-cover rounded" />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 m-1"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {error && <p className="text-red-500">{error}</p>}
      <SubmitButton>{isUploading ? 'Uploading...' : 'Create Raffle'}</SubmitButton>
    </form>
  )
}