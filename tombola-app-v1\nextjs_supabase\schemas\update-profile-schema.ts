import { z } from "zod";

export const PasswordSchema = z.object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: z.string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
        .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
        .regex(/[0-9]/, 'Password must contain at least one number'),
    confirmNewPassword: z.string()
}).refine((data: any) => data.newPassword === data.confirmNewPassword, {
    message: "Passwords don't match",
    path: ["confirmNewPassword"],
});

export const ProfileSchema = z.object({
    username: z.string().min(3).max(20).optional(),
    bio: z.string().max(500).optional(),
    profile_picture: z.any().optional(),
});
