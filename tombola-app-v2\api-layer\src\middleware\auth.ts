import { FastifyRequest, FastifyReply } from 'fastify';
import { supabaseService } from '../services/supabase';
import { AuthenticationError } from './errorHandler';
import { logger } from '../utils/logger';

declare module 'fastify' {
  interface FastifyRequest {
    user?: {
      id: string;
      email: string;
      [key: string]: any;
    };
  }
}

export const authMiddleware = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const authHeader = request.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AuthenticationError('Missing or invalid authorization header');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    if (!token) {
      throw new AuthenticationError('Missing access token');
    }

    // Verify token with Supabase
    const user = await supabaseService.verifyToken(token);
    
    if (!user) {
      throw new AuthenticationError('Invalid or expired token');
    }

    // Add user to request object
    request.user = {
      id: user.id,
      email: user.email || '',
      ...user.user_metadata,
    };

    logger.debug({
      userId: user.id,
      email: user.email,
      route: request.url,
      method: request.method,
    }, 'User authenticated successfully');

  } catch (error) {
    logger.error({
      error: error instanceof Error ? error.message : 'Unknown error',
      route: request.url,
      method: request.method,
      ip: request.ip,
    }, 'Authentication failed');

    if (error instanceof AuthenticationError) {
      throw error;
    }

    throw new AuthenticationError('Authentication failed');
  }
};

// Optional auth middleware - doesn't throw if no token provided
export const optionalAuthMiddleware = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const authHeader = request.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return; // No token provided, continue without user
    }

    const token = authHeader.substring(7);
    
    if (!token) {
      return; // Empty token, continue without user
    }

    // Verify token with Supabase
    const user = await supabaseService.verifyToken(token);
    
    if (user) {
      // Add user to request object if token is valid
      request.user = {
        id: user.id,
        email: user.email || '',
        ...user.user_metadata,
      };

      logger.debug({
        userId: user.id,
        email: user.email,
        route: request.url,
        method: request.method,
      }, 'Optional auth: User authenticated');
    }

  } catch (error) {
    logger.warn({
      error: error instanceof Error ? error.message : 'Unknown error',
      route: request.url,
      method: request.method,
    }, 'Optional auth: Token verification failed, continuing without user');
    
    // Don't throw error for optional auth, just continue without user
  }
};

// Admin role check middleware
export const requireAdminMiddleware = async (request: FastifyRequest, reply: FastifyReply) => {
  if (!request.user) {
    throw new AuthenticationError('Authentication required');
  }

  // Check if user has admin role (you can customize this logic)
  const isAdmin = request.user.role === 'admin' || request.user.is_admin === true;
  
  if (!isAdmin) {
    throw new AuthenticationError('Admin access required');
  }

  logger.debug({
    userId: request.user.id,
    route: request.url,
    method: request.method,
  }, 'Admin access granted');
};
