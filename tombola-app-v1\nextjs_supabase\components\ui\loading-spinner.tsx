import { Loader2 } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: number
  color?: string
}

export default function LoadingSpinner({ size = 24, color = "text-primary" }: LoadingSpinnerProps) {
  return (
    <div className="flex items-center justify-center w-full h-full">
      <Loader2 className={`animate-spin ${color}`} size={size} aria-hidden="true" />
      <span className="sr-only">Loading</span>
    </div>
  )
}

