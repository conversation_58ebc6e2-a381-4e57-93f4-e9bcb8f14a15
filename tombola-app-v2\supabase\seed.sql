-- Tombola V2 Seed Data
-- Sample data for development and testing

-- Insert sample categories
INSERT INTO public.category (id, name, description, sort_order) VALUES
    ('550e8400-e29b-41d4-a716-************', 'Electronics', 'Electronic devices and gadgets', 1),
    ('550e8400-e29b-41d4-a716-************', 'Fashion', 'Clothing, accessories, and fashion items', 2),
    ('550e8400-e29b-41d4-a716-************', 'Home & Garden', 'Home improvement and garden items', 3),
    ('550e8400-e29b-41d4-a716-446655440004', 'Sports & Outdoors', 'Sports equipment and outdoor gear', 4),
    ('550e8400-e29b-41d4-a716-446655440005', 'Books & Media', 'Books, movies, music, and games', 5);

-- Insert sample subcategories
INSERT INTO public.subcategory (id, category_id, name, description, sort_order) VALUES
    -- Electronics subcategories
    ('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Smartphones', 'Mobile phones and accessories', 1),
    ('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Laptops', 'Portable computers and accessories', 2),
    ('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Gaming', 'Gaming consoles and accessories', 3),
    
    -- Fashion subcategories
    ('660e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-************', 'Mens Clothing', 'Clothing for men', 1),
    ('660e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-************', 'Womens Clothing', 'Clothing for women', 2),
    ('660e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-************', 'Accessories', 'Fashion accessories', 3),
    
    -- Home & Garden subcategories
    ('660e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-************', 'Kitchen', 'Kitchen appliances and tools', 1),
    ('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Furniture', 'Home furniture', 2),
    ('660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 'Garden Tools', 'Gardening equipment', 3);

-- Insert sample charities
INSERT INTO public.charity (id, name, description, website, charity_number, is_verified) VALUES
    ('770e8400-e29b-41d4-a716-************', 'Local Food Bank', 'Supporting families in need with food assistance', 'https://localfoodbank.org', 'CHR001', true),
    ('770e8400-e29b-41d4-a716-************', 'Animal Rescue Centre', 'Rescuing and rehoming abandoned animals', 'https://animalrescue.org', 'CHR002', true),
    ('770e8400-e29b-41d4-a716-************', 'Children Education Fund', 'Providing educational resources to underprivileged children', 'https://childreneducation.org', 'CHR003', true);

-- Note: User data will be automatically created when users sign up through Supabase Auth
-- The handle_new_user() trigger will create corresponding records in the public.user table

-- Sample products (these would normally be created by authenticated users)
-- For development, we'll create some sample products with placeholder user IDs
-- In production, these would be created through the API by real users

-- Sample comments, tickets, and other data would be created through normal app usage
-- This seed file focuses on the foundational data (categories, subcategories, charities)
-- that needs to exist for the app to function properly

-- Create some sample notifications types for reference
-- (These would normally be created by the application logic)

-- Sample data for testing purposes only
-- Uncomment the following section if you need test data with actual user records

/*
-- Test user data (for development only)
-- Note: In production, users are created through Supabase Auth

INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at) VALUES
    ('880e8400-e29b-41d4-a716-************', '<EMAIL>', crypt('password123', gen_salt('bf')), NOW(), NOW(), NOW()),
    ('880e8400-e29b-41d4-a716-************', '<EMAIL>', crypt('password123', gen_salt('bf')), NOW(), NOW(), NOW());

-- Test products
INSERT INTO public.product (id, user_id, category_id, subcategory_id, product_name, description, price, ticket_cost, ticket_count, product_images) VALUES
    ('990e8400-e29b-41d4-a716-************', 
     '880e8400-e29b-41d4-a716-************', 
     '550e8400-e29b-41d4-a716-************', 
     '660e8400-e29b-41d4-a716-************',
     'iPhone 15 Pro', 
     'Brand new iPhone 15 Pro in Space Black', 
     999.99, 
     5.00, 
     200,
     ARRAY['https://example.com/iphone1.jpg', 'https://example.com/iphone2.jpg']),
    
    ('990e8400-e29b-41d4-a716-************', 
     '880e8400-e29b-41d4-a716-************', 
     '550e8400-e29b-41d4-a716-************', 
     '660e8400-e29b-41d4-a716-************',
     'MacBook Air M3', 
     'Latest MacBook Air with M3 chip, 16GB RAM, 512GB SSD', 
     1299.99, 
     10.00, 
     130,
     ARRAY['https://example.com/macbook1.jpg', 'https://example.com/macbook2.jpg']);
*/

-- Create indexes for better performance on seed data
CREATE INDEX IF NOT EXISTS idx_category_sort_order ON public.category(sort_order);
CREATE INDEX IF NOT EXISTS idx_subcategory_sort_order ON public.subcategory(sort_order);
CREATE INDEX IF NOT EXISTS idx_charity_verified ON public.charity(is_verified) WHERE is_verified = true;
