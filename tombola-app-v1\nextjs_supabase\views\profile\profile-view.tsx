'use client'

import {useEffect, useState} from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {AlertCircle, CheckCircle2, User} from 'lucide-react'
import {PasswordSchema, ProfileSchema } from '@/schemas/update-profile-schema'
import {changePassword, updateProfile} from '@/app/(dashboard)/profile/actions'
import {Avatar, AvatarFallback, AvatarImage} from '@/components/ui/avatar'


type PasswordFormData = z.infer<typeof PasswordSchema>;
type ProfileFormData = z.infer<typeof ProfileSchema>;

interface InitialData {
  username: string
  bio: string | null
  profile_picture: string | null
  followersCount: number
  followingCount: number
}

export default function EditProfileForm({ initialData }: { initialData: InitialData }) {

    // ** States
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null)
  const [passwordError, setPasswordError] = useState<string | null>(null)
  const [profileSuccess, setProfileSuccess] = useState<string | null>(null)
  const [profileError, setProfileError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // ** Resolvers
  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(PasswordSchema)
  });

  const profileForm = useForm<ProfileFormData>({
    resolver: zodResolver(ProfileSchema),
    defaultValues: {
      username: initialData?.username,
      bio: initialData?.bio ?? '',
    }
  });

  // ** Handlers
  const onProfileSubmit = async (data: ProfileFormData) => {
    setIsLoading(true)
    setProfileError(null)
    setProfileSuccess(null)

    try {
      const result = await updateProfile({
        username: data.username,
        bio: data.bio,
        profile_picture: data.profile_picture
      })

      if (result.error) {
        setProfileError(result.error)
      } else {
        setProfileSuccess('Profile updated successfully')
      }
    } catch (error: any) {
      setProfileError('Failed to update profile')
    } finally {
      setIsLoading(false)
    }
  }

  const onPasswordSubmit = async (data: PasswordFormData) => {
    setIsLoading(true)
    setPasswordError(null)
    setPasswordSuccess(null)

    try {
      const result: any = await changePassword({
        currentPassword: data.currentPassword,
        newPassword: data.newPassword
      })

      if (result.error) {
        setPasswordError(result.error)
      } else {
        setPasswordSuccess('Password changed successfully')
        passwordForm.reset()
      }
    } catch (error: any) {
      setPasswordError(error.message || 'Failed to change password')
    } finally {
      setIsLoading(false)
    }
  }


  const profilePicture = profileForm.watch('profile_picture')

  useEffect(() => {
    if (profilePicture?.[0]) {
      const file = profilePicture[0]
      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }, [profilePicture])

  return (
    <div className="h-screen bg-gray-100 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl space-y-8">
        <Card>
          <CardContent className="py-6">
            <div className="flex flex-col items-center gap-4">
              <Avatar className="w-11 h-11">
                <AvatarImage
                    src={initialData.profile_picture || undefined}
                    alt={initialData.username}
                    className="object-cover"
                />
                <AvatarFallback className="bg-gray-100">
                  <User className="w-12 h-12 text-gray-400" />
                </AvatarFallback>
              </Avatar>

              <div className="text-center">
                <h2 className="text-xl font-semibold">@{initialData.username}</h2>
                <div className="flex gap-4 justify-center mt-2 text-sm text-gray-600">
                  <span>{initialData.followersCount ?? 0} followers</span>
                  <span>{initialData.followingCount?? 0} following</span>
                </div>
                {initialData.bio && (
                    <p className="mt-2 text-sm text-gray-600">{initialData.bio}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Change Password</CardTitle>
          </CardHeader>
          <CardContent>
            {passwordError && (
                <Alert variant="destructive" className="mb-6">
                  <AlertCircle className="h-4 w-4"/>
                  <AlertDescription>{passwordError}</AlertDescription>
                </Alert>
            )}
            {passwordSuccess && (
                <Alert className="mb-6 bg-green-50 border-green-200">
                  <CheckCircle2 className="h-4 w-4 text-green-600"/>
                  <AlertDescription className="text-green-700">{passwordSuccess}</AlertDescription>
                </Alert>
            )}

            <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-4">
              <div>
                <Label htmlFor="currentPassword">Current Password</Label>
                <Input
                    id="currentPassword"
                    type="password"
                    {...passwordForm.register('currentPassword')}
                />
                {passwordForm.formState.errors.currentPassword && (
                    <p className="mt-1 text-sm text-red-500">{passwordForm.formState.errors.currentPassword.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="newPassword">New Password</Label>
                <Input
                    id="newPassword"
                    type="password"
                    {...passwordForm.register('newPassword')}
                />
                {passwordForm.formState.errors.newPassword && (
                    <p className="mt-1 text-sm text-red-500">{passwordForm.formState.errors.newPassword.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="confirmNewPassword">Confirm New Password</Label>
                <Input
                    id="confirmNewPassword"
                    type="password"
                    {...passwordForm.register('confirmNewPassword')}
                />
                {passwordForm.formState.errors.confirmNewPassword && (
                    <p className="mt-1 text-sm text-red-500">{passwordForm.formState.errors.confirmNewPassword.message}</p>
                )}
              </div>

              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Updating...' : 'Update Password'}
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card className='mt-5'>
          <CardHeader>
            <CardTitle>Edit Profile</CardTitle>
          </CardHeader>
          <CardContent>
            {profileError && (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{profileError}</AlertDescription>
              </Alert>
            )}
            {profileSuccess && (
              <Alert className="mb-6 bg-green-50 border-green-200">
                <CheckCircle2 className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-700">{profileSuccess}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-4">
              <div>
                <Label htmlFor="username">Username</Label>
                <Input
                    id="username"
                    {...profileForm.register('username')}
                />
                {profileForm.formState.errors.username && (
                    <p className="mt-1 text-sm text-red-500">{profileForm.formState.errors.username.message}</p>
                )}
              </div>

              <div className="space-y-4">
                <Label htmlFor="profile_picture">Profile Picture</Label>
                <div className="flex items-center gap-4">
                  {(imagePreview || initialData?.profile_picture) && (
                      <div className="relative w-10 h-10">
                        <img
                            src={imagePreview || initialData?.profile_picture || ''}
                            alt="Profile preview"
                            className="rounded-full w-full h-full object-cover"
                        />
                      </div>
                  )}
                  <Input
                      id="profile_picture"
                      type="file"
                      accept="image/*"
                      fileStyle
                      {...profileForm.register('profile_picture')}
                      className="flex-1"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                    id="bio"
                    {...profileForm.register('bio')}
                />
                {profileForm.formState.errors.bio && (
                    <p className="mt-1 text-sm text-red-500">{profileForm.formState.errors.bio.message}</p>
                )}
              </div>

              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Updating...' : 'Update Profile'}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
