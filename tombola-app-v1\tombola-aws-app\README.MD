## 📘 README.md (Starter)

# Tombola Raffle App (Backend)

This is a serverless raffle system built on AWS Lambda, SNS, SES, and Docker.

## Features
- Buy raffle tickets with Stripe
- Randomly draw winners
- Escrow flow with delivery confirmation
- Email notifications for winner/seller

## Architecture
- Event-driven (pub/sub) via SNS
- Supabase for product/ticket storage
- Stripe for payments

## Run Locally
```bash
cd tombola-aws-app/lambda_functions/buy_ticket
docker build -t buy-ticket .
docker run --rm -e AWS_REGION=ap-southeast-2 buy-ticket
```

For full end-to-end local emulation, use:
```bash
cd tombola-app
sam local invoke BuyTicketFunction --event events/buy-ticket.json
```

## Deploy
### 1. Prerequisites
- AWS CLI installed and configured (`aws configure`)
- Docker running
- SAM CLI installed (`brew install aws-sam-cli` or via pip)

### 2. Run the Deployment Script
```bash
cd tombola-aws-app
./scripts/deploy.sh
```
This will:
- Build Docker images for each Lambda
- Push to ECR (auto-creates repos if needed)
- Deploy all resources defined in `template.yaml`

### 3. Resources Are Auto-Created
✅ **Yes — `template.yaml` creates everything for you:**
- SNS Topics
- Lambda Functions
- API Gateway endpoints

You don't need to manually create any resources via the AWS Console.