'use server'

import { SupabaseClient } from '@supabase/supabase-js'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { Database } from '@/types/supabase'

export async function getUserProfile(slug: string, supabase: SupabaseClient<Database>) {
  const { data: profile, error } = await supabase
      .from('user')
      .select('*')
      .eq('username', slug)
      .single()

  if (error) return null

  // Get follower and following counts
  const { count: followersCount } = await supabase
      .from('follow')
      .select('*', { count: 'exact' })
      .eq('followee_id', profile.id)

  const { count: followingCount } = await supabase
      .from('follow')
      .select('*', { count: 'exact' })
      .eq('follower_id', profile.id)

  // Check if current user is following this profile
  const { data: { user } } = await supabase.auth.getUser()
  const { data: isFollowing } = await supabase
      .from('follow')
      .select('*')
      .eq('follower_id', user?.id ?? '')
      .eq('followee_id', profile.id)
      .single()

  return {
    ...profile,
    followersCount: followersCount || 0,
    followingCount: followingCount || 0,
    isFollowing: !!isFollowing
  }
}

export async function followUser(userId: string) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_KEY!,
        {
          cookies: {
            get(name: string) {
              return cookieStore.get(name)?.value
            },
            set(name: string, value: string, options: any) {
              cookieStore.set({ name, value, ...options })
            },
            remove(name: string, options: any) {
              cookieStore.set({ name, value: '', ...options })
            },
          },
        }
    )

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) return { error: 'Not authenticated' }

    // Check if already following
    const { data: existingFollow } = await supabase
        .from('follow')
        .select('*')
        .eq('follower_id', user.id)
        .eq('followee_id', userId)
        .single()

    if (existingFollow) {
      return { error: 'Already following this user' }
    }

    // Create new follow relationship
    const { error } = await supabase
        .from('follow')
        .insert({
          follower_id: user.id,
          followee_id: userId
        })

    if (error) return { error: 'Failed to follow user' }
    return { success: true }
  } catch (error) {
    return { error: 'An unexpected error occurred' }
  }
}

export async function unfollowUser(userId: string) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient<Database>(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_KEY!,
        {
          cookies: {
            get(name: string) {
              return cookieStore.get(name)?.value
            },
            set(name: string, value: string, options: any) {
              cookieStore.set({ name, value, ...options })
            },
            remove(name: string, options: any) {
              cookieStore.set({ name, value: '', ...options })
            },
          },
        }
    )

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) return { error: 'Not authenticated' }

    const { error } = await supabase
        .from('follow')
        .delete()
        .eq('follower_id', user.id)
        .eq('followee_id', userId)

    if (error) return { error: 'Failed to unfollow user' }
    return { success: true }
  } catch (error) {
    return { error: 'An unexpected error occurred' }
  }
}
