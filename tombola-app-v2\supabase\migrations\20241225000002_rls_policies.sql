-- Tombola V2 Row Level Security Policies and Grants
-- Comprehensive security setup for all tables

-- Grant permissions to anon and authenticated users
-- Users table
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."user" TO anon;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."user" TO authenticated;
GRANT ALL ON TABLE "public"."user" TO service_role;

-- Categories table
GRANT SELECT ON TABLE "public"."category" TO anon;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."category" TO authenticated;
GRANT ALL ON TABLE "public"."category" TO service_role;

-- Subcategories table
GRANT SELECT ON TABLE "public"."subcategory" TO anon;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."subcategory" TO authenticated;
GRANT ALL ON TABLE "public"."subcategory" TO service_role;

-- Products table
GRANT SELECT ON TABLE "public"."product" TO anon;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."product" TO authenticated;
GRANT ALL ON TABLE "public"."product" TO service_role;

-- Tickets table
GRANT SELECT ON TABLE "public"."ticket" TO anon;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."ticket" TO authenticated;
GRANT ALL ON TABLE "public"."ticket" TO service_role;

-- Payments table
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."payment" TO authenticated;
GRANT ALL ON TABLE "public"."payment" TO service_role;

-- Comments table
GRANT SELECT ON TABLE "public"."comment" TO anon;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."comment" TO authenticated;
GRANT ALL ON TABLE "public"."comment" TO service_role;

-- Follows table
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."follow" TO authenticated;
GRANT ALL ON TABLE "public"."follow" TO service_role;

-- Notifications table
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."notification" TO authenticated;
GRANT ALL ON TABLE "public"."notification" TO service_role;

-- Messages table
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."message" TO authenticated;
GRANT ALL ON TABLE "public"."message" TO service_role;

-- Charity table
GRANT SELECT ON TABLE "public"."charity" TO anon;
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."charity" TO authenticated;
GRANT ALL ON TABLE "public"."charity" TO service_role;

-- Charity membership table
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE "public"."charity_membership" TO authenticated;
GRANT ALL ON TABLE "public"."charity_membership" TO service_role;

-- Row Level Security Policies

-- Users table policies
CREATE POLICY "Users can view all profiles" ON "public"."user"
    FOR SELECT TO anon, authenticated
    USING (true);

CREATE POLICY "Users can update own profile" ON "public"."user"
    FOR UPDATE TO authenticated
    USING (auth.uid() = id)
    WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON "public"."user"
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = id);

-- Categories table policies (public read, admin write)
CREATE POLICY "Anyone can view categories" ON "public"."category"
    FOR SELECT TO anon, authenticated
    USING (is_active = true);

CREATE POLICY "Admins can manage categories" ON "public"."category"
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM "public"."user" 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Subcategories table policies
CREATE POLICY "Anyone can view subcategories" ON "public"."subcategory"
    FOR SELECT TO anon, authenticated
    USING (is_active = true);

CREATE POLICY "Admins can manage subcategories" ON "public"."subcategory"
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM "public"."user" 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Products table policies
CREATE POLICY "Anyone can view active products" ON "public"."product"
    FOR SELECT TO anon, authenticated
    USING (status = 'active');

CREATE POLICY "Users can create products" ON "public"."product"
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own products" ON "public"."product"
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own products" ON "public"."product"
    FOR DELETE TO authenticated
    USING (auth.uid() = user_id);

-- Tickets table policies
CREATE POLICY "Anyone can view tickets for active products" ON "public"."ticket"
    FOR SELECT TO anon, authenticated
    USING (
        EXISTS (
            SELECT 1 FROM "public"."product" 
            WHERE id = product_id AND status = 'active'
        )
    );

CREATE POLICY "Users can create tickets" ON "public"."ticket"
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can view own tickets" ON "public"."ticket"
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

-- Payments table policies
CREATE POLICY "Users can view own payments" ON "public"."payment"
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can create payments" ON "public"."payment"
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own payments" ON "public"."payment"
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Comments table policies
CREATE POLICY "Anyone can view active comments" ON "public"."comment"
    FOR SELECT TO anon, authenticated
    USING (is_active = true);

CREATE POLICY "Users can create comments" ON "public"."comment"
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own comments" ON "public"."comment"
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own comments" ON "public"."comment"
    FOR DELETE TO authenticated
    USING (auth.uid() = user_id);

-- Follows table policies
CREATE POLICY "Users can view follows" ON "public"."follow"
    FOR SELECT TO authenticated
    USING (true);

CREATE POLICY "Users can create follows" ON "public"."follow"
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = follower_id);

CREATE POLICY "Users can delete own follows" ON "public"."follow"
    FOR DELETE TO authenticated
    USING (auth.uid() = follower_id);

-- Notifications table policies
CREATE POLICY "Users can view own notifications" ON "public"."notification"
    FOR SELECT TO authenticated
    USING (auth.uid() = user_id);

CREATE POLICY "Users can update own notifications" ON "public"."notification"
    FOR UPDATE TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Messages table policies
CREATE POLICY "Users can view own messages" ON "public"."message"
    FOR SELECT TO authenticated
    USING (auth.uid() = sender_id OR auth.uid() = receiver_id);

CREATE POLICY "Users can send messages" ON "public"."message"
    FOR INSERT TO authenticated
    WITH CHECK (auth.uid() = sender_id);

CREATE POLICY "Users can update own sent messages" ON "public"."message"
    FOR UPDATE TO authenticated
    USING (auth.uid() = sender_id)
    WITH CHECK (auth.uid() = sender_id);

-- Charity table policies
CREATE POLICY "Anyone can view verified charities" ON "public"."charity"
    FOR SELECT TO anon, authenticated
    USING (is_verified = true);

CREATE POLICY "Admins can manage charities" ON "public"."charity"
    FOR ALL TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM "public"."user" 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Charity membership table policies
CREATE POLICY "Users can view charity memberships" ON "public"."charity_membership"
    FOR SELECT TO authenticated
    USING (true);

CREATE POLICY "Users can manage own charity memberships" ON "public"."charity_membership"
    FOR ALL TO authenticated
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);
