"use client";
import { CategoryCard } from "@/components/categoryCard";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { useCategories } from "@/hooks/useCategories";

export default function ExplorePage() {
  const { categories, loading } = useCategories();

  return (
    <>
      {loading ? (
        <LoadingSpinner />
      ) : (
        categories &&
        categories.length > 0 &&
        categories.map((category, index) => (
          <CategoryCard
            key={index}
            id={category.id}
            name={category.name ?? ""}
            image_url={category.image ?? ""}
          />
        ))
      )}
    </>
  );
}
