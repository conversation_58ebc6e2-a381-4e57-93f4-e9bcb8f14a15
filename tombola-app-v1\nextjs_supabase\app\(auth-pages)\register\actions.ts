'use server';

import { createClient } from '@supabase/supabase-js';
import { revalidatePath } from 'next/cache';
import { Database } from '@/types/supabase'

if (!process.env.NEXT_PUBLIC_SUPABASE_URL) throw new Error('Missing SUPABASE_URL');
if (!process.env.SUPABASE_KEY) throw new Error('Missing SUPABASE_KEY');

const supabase = createClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_KEY,
    {
        auth: {
            autoRefreshToken: false,
            persistSession: false
        }
    }
);

export async function validateUsername(username: string): Promise<boolean> {
    try {
        const { count, error } = await supabase
            .from('user')
            .select('*', { count: 'exact', head: true })
            .eq('username', username);

        if (error) {
            console.error('Error validating username:', error);
            return false;
        }

        return count === 0;
    } catch (error) {
        console.error('Error validating username:', error);
        return false;
    }
}

export async function validateEmail(email: string): Promise<boolean> {
    try {
        // Check auth.users table
        const { data: users, error: err } = await supabase
            .from('user') // Your custom users table
            .select('*') // Adjust to select only the fields you need
            .eq('email', email); // Filter by email

        if (err) {
            console.error('Error validating email in users table:', err);
            return false;
        }

        // Also check public.user table
        const { count, error } = await supabase
            .from('user')
            .select('*', { count: 'exact', head: true })
            .eq('email', email);

        if (error) {
            console.error('Error validating email in public.user:', error);
            return false;
        }

        return count === 0;
    } catch (error) {
        console.error('Error validating email:', error);
        return false;
    }
}

export async function createUser(userData: any) {
    try {
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
            email: userData.email,
            password: userData.password,
            email_confirm: true,
            user_metadata: {
                username: userData.username
            }
        });

        if (authError) throw authError;
        if (!authData.user) throw new Error('User creation failed');

        let profileImagePath = null;
        if (userData.profile_picture && userData.profile_picture?.[0]) {
            const file = userData.profile_picture[0];
            const fileExt = file.name.split('.').pop();
            const fileName = `${authData.user.id}.${fileExt}`;
            const filePath = `${authData.user.id}/${fileName}`;

            const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                throw new Error('Invalid file type. Please upload a JPEG, PNG, or GIF image.');
            }

            if (file.size > 5 * 1024 * 1024) {
                throw new Error('File size too large. Maximum size is 5MB.');
            }

            const { error: uploadError } = await supabase.storage
                .from('profile')
                .upload(filePath, file, {
                    cacheControl: '3600',
                    upsert: true
                });

            if (uploadError) throw uploadError;

            profileImagePath = filePath;
        }

        const { error: profileError } = await supabase
            .from('user')
            .insert({
                id: authData.user.id,
                email: userData.email,
                username: userData.username,
                bio: userData.bio || null,
                profile_picture: profileImagePath,
                role: 'user',
                created_at: new Date().toISOString()
            });

        if (profileError) {
            await supabase.auth.admin.deleteUser(authData.user.id);
            throw profileError;
        }

        const { error: signInError } = await supabase.auth.signInWithPassword({
            email: userData.email,
            password: userData.password,
        });

        if (signInError) {
            throw signInError;
        }

        revalidatePath('/');
        return { success: true, userId: authData.user.id };
    } catch (error) {
        console.error('Error creating user:', error);
        throw error;
    }
}
