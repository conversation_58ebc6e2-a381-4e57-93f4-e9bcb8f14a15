'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { formatDistanceToNow } from 'date-fns'
import { User, Search, Mail } from 'lucide-react'

import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"

interface Conversation {
    otherUser: {
        username: string
        profile_picture: string | null
    }
    lastMessage: {
        content: string | null
        created_at: string
        is_read: boolean
        is_sent_by_me: boolean
    }
}

interface InboxViewProps {
    conversations: Conversation[]
}

export default function InboxView({ conversations }: InboxViewProps) {
    const router = useRouter()
    const [searchQuery, setSearchQuery] = useState('')

    const handleConversationClick = (username: string) => {
        router.push(`/messages/${username}`)
    }

    const filteredConversations = conversations.filter(conversation =>
        conversation.otherUser.username.toLowerCase().includes(searchQuery.toLowerCase())
    )

    return (
        <Card className="w-full max-w-2xl mx-auto">
            <CardHeader>
                <CardTitle className="text-2xl font-bold">Inbox</CardTitle>
                <div className="relative">
                    <Input
                        placeholder="Search conversations..."
                        className="pl-8"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </div>
            </CardHeader>
            <CardContent>
                <ScrollArea className="h-[500px] pr-4 cursor-pointer">
                    {filteredConversations.length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground">
                            <Mail className="h-12 w-12 mb-4" />
                            <p className="text-lg font-medium">No messages yet</p>
                            <p className="text-sm">Start a conversation or search for users</p>
                        </div>
                    ) : (
                        filteredConversations.map((conversation) => (
                            <div
                                key={conversation.otherUser.username}
                                className="flex items-center gap-4 p-4 rounded-lg hover:bg-accent cursor-pointer transition-colors"
                                onClick={() => handleConversationClick(conversation.otherUser.username)}
                            >
                                <Avatar className="w-12 h-12">
                                    <AvatarImage
                                        src={conversation.otherUser.profile_picture || undefined}
                                        alt={conversation.otherUser.username}
                                    />
                                    <AvatarFallback>
                                        <User className="w-6 h-6" />
                                    </AvatarFallback>
                                </Avatar>

                                <div className="flex-1 min-w-0 cursor-pointer">
                                    <div className="flex items-center justify-between cursor-pointer">
                                        <p className="font-medium">@{conversation.otherUser.username}</p>
                                        <span className="text-xs text-muted-foreground">
                      {formatDistanceToNow(new Date(conversation.lastMessage.created_at), { addSuffix: true })}
                    </span>
                                    </div>

                                    <div className="flex items-center gap-2 mt-1">
                                        {conversation.lastMessage.is_sent_by_me && (
                                            <span className="text-xs text-muted-foreground">You:</span>
                                        )}
                                        <p className="text-sm text-muted-foreground truncate">
                                            {conversation.lastMessage.content || 'Sent an image'}
                                        </p>
                                        {!conversation.lastMessage.is_read && !conversation.lastMessage.is_sent_by_me && (
                                            <Badge variant="default" className="text-xs">New</Badge>
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))
                    )}
                </ScrollArea>
            </CardContent>
        </Card>
    )
}

