'use server'
import { cookies } from 'next/headers'
import { revalidatePath } from 'next/cache'
import { createServerClient } from '@supabase/ssr'

export async function changePassword(data: {
  currentPassword: string
  newPassword: string
}) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_KEY!,
        {
          cookies: {
            get(name: string) {
              return cookieStore.get(name)?.value
            },
            set(name: string, value: string, options: any) {
              cookieStore.set({ name, value, ...options })
            },
            remove(name: string, options: any) {
              cookieStore.set({ name, value: '', ...options })
            },
          },
        }
    )

    const { error } = await supabase.auth.updateUser({
      password: data.newPassword
    })

    if (error) throw error

    revalidatePath('/profile')
    return { success: true }
  } catch (error) {
    console.error('Change password error:', error)
    throw error
  }
}

export async function updateProfile(data: {
  username?: string
  bio?: string
  profile_picture?: FileList
}) {
  try {
    const cookieStore = await cookies()
    const supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_KEY!,
        {
          cookies: {
            get(name: string) {
              return cookieStore.get(name)?.value
            },
            set(name: string, value: string, options: any) {
              cookieStore.set({ name, value, ...options })
            },
            remove(name: string, options: any) {
              cookieStore.set({ name, value: '', ...options })
            },
          },
        }
    )

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError) throw userError
    if (!user) throw new Error('User not found')

    if (data.username && data.username !== '') {
      const { data: existingUser, error: usernameCheckError } = await supabase
          .from('user')
          .select('username')
          .eq('username', data.username)
          .neq('id', user.id)
          .single()

      if (usernameCheckError && usernameCheckError.code !== 'PGRST116') {
        return { error: 'Error checking username availability' }
      }

      if (existingUser) {
        return { error: 'Username is already taken' }
      }
    }


    let profileImagePath = null
    if (data.profile_picture && data.profile_picture[0]) {
      const file = data.profile_picture[0]
      const fileExt = file.name.split('.').pop()
      const fileName = `${user.id}/${user.id}.${fileExt}`

      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif']
      if (!allowedTypes.includes(file.type)) {
        throw new Error('Invalid file type. Please upload a JPEG, PNG, or GIF image.')
      }

      if (file.size > 5 * 1024 * 1024) {
        throw new Error('File size too large. Maximum size is 5MB.')
      }

      // First, convert the File to ArrayBuffer
      const arrayBuffer = await file.arrayBuffer()
      const buffer = new Uint8Array(arrayBuffer)

      const { error: uploadError } = await supabase.storage
          .from('profile')
          .upload(fileName, buffer, {
            cacheControl: '3600',
            upsert: true,
            contentType: file.type
          })

      if (uploadError) throw uploadError
      profileImagePath = fileName
    }

    // Build the update data
    const updateData: Record<string, any> = {}

    if (data.username && data.username !== '') {
      updateData.username = data.username
    }

    if (data.bio && data.bio !== '') {
      updateData.bio = data.bio
    }

    if (profileImagePath) {
      updateData.profile_picture = profileImagePath
    }

    if (Object.keys(updateData).length > 0) {
      const { error: profileError } = await supabase
          .from('user')
          .update(updateData)
          .eq('id', user.id)

      if (profileError) return { error: 'Error updating profile' }
    }

    revalidatePath('/profile')
    revalidatePath('/dashboard')
    return { success: true }
  } catch (error) {
    console.error('Update profile error:', error)
    return { error: 'An unexpected error occurred' }
  }
}


