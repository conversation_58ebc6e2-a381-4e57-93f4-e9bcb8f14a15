import Image from "next/image";
import Link from "next/link";

interface CategoryCardProps {
  id: string;
  image_url: string;
  name: string;
}

export function CategoryCard({
  id,
  image_url,
  name
}: CategoryCardProps) {
  return (
<Link 
      href={`/explore/${id}`} 
      className="border rounded-lg shadow-subtle max-w-lg relative aspect-square group overflow-hidden"
    >
      <div className="absolute inset-0 bg-black bg-opacity-40 transition-opacity duration-300 group-hover:opacity-0 z-10"></div>
      <Image src={image_url} alt={name} fill className="object-cover transition-transform duration-300 group-hover:scale-110"/>
      <div className="absolute inset-0 flex items-center justify-center z-20">
        <span className="text-white text-lg font-semibold">{name}</span>
      </div>
    </Link>
  );
}
