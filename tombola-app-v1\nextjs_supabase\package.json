{"private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "update-types": "npx supabase gen types --lang=typescript > types/supabase.ts"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@stripe/connect-js": "^3.3.18", "@stripe/react-connect-js": "^3.3.19", "@supabase/ssr": "latest", "@supabase/supabase-js": "^2.46.1", "@tabler/icons-react": "^3.22.0", "autoprefixer": "10.4.17", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.11.17", "geist": "^1.2.1", "lucide-react": "^0.436.0", "next": "latest", "next-themes": "^0.3.0", "prettier": "^3.3.3", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.53.2", "yet-another-react-lightbox": "^3.21.7", "zod": "^3.23.8", "stripe": "^17.4.0"}, "devDependencies": {"@types/node": "20.10.6", "@types/react": "18.2.46", "@types/react-dom": "18.2.18", "postcss": "8.4.33", "tailwind-merge": "^2.5.4", "tailwindcss": "3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "5.3.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}