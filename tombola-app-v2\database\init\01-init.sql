-- Tombola V2 Database Initialization
-- This script initializes the PostgreSQL database with the complete schema

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE tombola_db'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'tombola_db')\gexec

-- Connect to the database
\c tombola_db;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON>reate custom types
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('user', 'admin', 'moderator');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE product_status AS ENUM ('active', 'completed', 'cancelled');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create auth schema (simplified version of Supabase auth)
CREATE SCHEMA IF NOT EXISTS auth;

-- Create auth.users table (simplified version)
CREATE TABLE IF NOT EXISTS auth.users (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    email varchar(255) UNIQUE NOT NULL,
    encrypted_password varchar(255),
    email_confirmed_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    last_sign_in_at timestamp with time zone,
    raw_app_meta_data jsonb,
    raw_user_meta_data jsonb,
    is_super_admin boolean DEFAULT false,
    role varchar(255) DEFAULT 'authenticated'
);

-- Create public schema tables
-- Users table (extends auth.users)
CREATE TABLE IF NOT EXISTS public."user" (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    username varchar(50) UNIQUE,
    email varchar(255),
    profile_picture varchar(500),
    bio text,
    role user_role NOT NULL DEFAULT 'user',
    is_active boolean NOT NULL DEFAULT true,
    last_login timestamp with time zone
);

-- Categories table
CREATE TABLE IF NOT EXISTS public.category (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    name varchar(100) NOT NULL,
    description text,
    is_active boolean NOT NULL DEFAULT true,
    sort_order integer DEFAULT 0
);

-- Subcategories table
CREATE TABLE IF NOT EXISTS public.subcategory (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    category_id uuid NOT NULL REFERENCES public.category(id) ON DELETE CASCADE,
    name varchar(100) NOT NULL,
    description text,
    is_active boolean NOT NULL DEFAULT true,
    sort_order integer DEFAULT 0
);

-- Products table
CREATE TABLE IF NOT EXISTS public.product (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    user_id uuid NOT NULL REFERENCES public."user"(id) ON DELETE CASCADE,
    category_id uuid REFERENCES public.category(id) ON DELETE SET NULL,
    subcategory_id uuid REFERENCES public.subcategory(id) ON DELETE SET NULL,
    product_name varchar(255) NOT NULL,
    description text,
    price decimal(10,2) NOT NULL,
    ticket_cost decimal(10,2) NOT NULL,
    ticket_count integer NOT NULL,
    tickets_sold integer NOT NULL DEFAULT 0,
    product_images text[],
    status product_status NOT NULL DEFAULT 'active',
    raffle_drawn boolean NOT NULL DEFAULT false,
    product_sent boolean NOT NULL DEFAULT false,
    product_delivered boolean NOT NULL DEFAULT false,
    winner_id uuid REFERENCES public."user"(id) ON DELETE SET NULL,
    draw_date timestamp with time zone,
    fulfillment_method varchar(50) DEFAULT 'shipping'
);

-- Tickets table
CREATE TABLE IF NOT EXISTS public.ticket (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    purchase_date timestamp with time zone NOT NULL DEFAULT now(),
    user_id uuid NOT NULL REFERENCES public."user"(id) ON DELETE CASCADE,
    product_id uuid NOT NULL REFERENCES public.product(id) ON DELETE CASCADE,
    ticket_number integer NOT NULL,
    is_winner boolean NOT NULL DEFAULT false,
    UNIQUE(product_id, ticket_number)
);

-- Payments table
CREATE TABLE IF NOT EXISTS public.payment (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    user_id uuid NOT NULL REFERENCES public."user"(id) ON DELETE CASCADE,
    product_id uuid NOT NULL REFERENCES public.product(id) ON DELETE CASCADE,
    ticket_ids uuid[] NOT NULL,
    amount decimal(10,2) NOT NULL,
    currency varchar(3) NOT NULL DEFAULT 'GBP',
    status payment_status NOT NULL DEFAULT 'pending',
    stripe_payment_intent_id varchar(255),
    stripe_charge_id varchar(255),
    payment_method varchar(50),
    processed_at timestamp with time zone
);

-- Comments table
CREATE TABLE IF NOT EXISTS public.comment (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    content text NOT NULL,
    user_id uuid NOT NULL REFERENCES public."user"(id) ON DELETE CASCADE,
    product_id uuid NOT NULL REFERENCES public.product(id) ON DELETE CASCADE,
    is_active boolean NOT NULL DEFAULT true
);

-- Follows table
CREATE TABLE IF NOT EXISTS public.follow (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    follower_id uuid NOT NULL REFERENCES public."user"(id) ON DELETE CASCADE,
    followee_id uuid NOT NULL REFERENCES public."user"(id) ON DELETE CASCADE,
    UNIQUE(follower_id, followee_id)
);

-- Notifications table
CREATE TABLE IF NOT EXISTS public.notification (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    user_id uuid NOT NULL REFERENCES public."user"(id) ON DELETE CASCADE,
    title varchar(255),
    message text NOT NULL,
    type varchar(50) NOT NULL DEFAULT 'general',
    read boolean NOT NULL DEFAULT false,
    read_at timestamp with time zone,
    data jsonb
);

-- Messages table
CREATE TABLE IF NOT EXISTS public.message (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    sender_id uuid NOT NULL REFERENCES public."user"(id) ON DELETE CASCADE,
    receiver_id uuid NOT NULL REFERENCES public."user"(id) ON DELETE CASCADE,
    content text,
    image_url varchar(500),
    read_at timestamp with time zone
);

-- Charity table
CREATE TABLE IF NOT EXISTS public.charity (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    updated_at timestamp with time zone NOT NULL DEFAULT now(),
    name varchar(255) NOT NULL,
    description text,
    logo varchar(500),
    business_address text,
    website varchar(500),
    charity_number varchar(50),
    is_verified boolean NOT NULL DEFAULT false
);

-- Charity membership table
CREATE TABLE IF NOT EXISTS public.charity_membership (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone NOT NULL DEFAULT now(),
    user_id uuid NOT NULL REFERENCES public."user"(id) ON DELETE CASCADE,
    charity_id uuid NOT NULL REFERENCES public.charity(id) ON DELETE CASCADE,
    role varchar(50) NOT NULL DEFAULT 'member',
    is_active boolean NOT NULL DEFAULT true,
    UNIQUE(user_id, charity_id)
);
