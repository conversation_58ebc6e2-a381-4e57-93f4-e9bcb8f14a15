-- Tombola V2 Database Functions and Triggers
-- Connect to the database
\c tombola_db;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to handle user creation from auth.users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public."user" (id, email, created_at, updated_at)
    VALUES (NEW.id, NEW.email, NOW(), NOW());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update tickets_sold when tickets are purchased
CREATE OR REPLACE FUNCTION update_tickets_sold()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.product 
        SET tickets_sold = tickets_sold + 1,
            updated_at = NOW()
        WHERE id = NEW.product_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.product 
        SET tickets_sold = tickets_sold - 1,
            updated_at = NOW()
        WHERE id = OLD.product_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to assign ticket numbers automatically
CREATE OR REPLACE FUNCTION assign_ticket_number()
RETURNS TRIGGER AS $$
BEGIN
    -- Get the next ticket number for this product
    SELECT COALESCE(MAX(ticket_number), 0) + 1
    INTO NEW.ticket_number
    FROM public.ticket
    WHERE product_id = NEW.product_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to validate ticket purchase limits
CREATE OR REPLACE FUNCTION validate_ticket_purchase()
RETURNS TRIGGER AS $$
DECLARE
    available_tickets INTEGER;
    product_status_val product_status;
BEGIN
    -- Check if product is active and has available tickets
    SELECT 
        (ticket_count - tickets_sold),
        status
    INTO available_tickets, product_status_val
    FROM public.product
    WHERE id = NEW.product_id;
    
    -- Validate product status
    IF product_status_val != 'active' THEN
        RAISE EXCEPTION 'Cannot purchase tickets for inactive product';
    END IF;
    
    -- Validate ticket availability
    IF available_tickets <= 0 THEN
        RAISE EXCEPTION 'No tickets available for this product';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to create notification
CREATE OR REPLACE FUNCTION create_notification(
    p_user_id UUID,
    p_title VARCHAR(255),
    p_message TEXT,
    p_type VARCHAR(50) DEFAULT 'general',
    p_data JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    notification_id UUID;
BEGIN
    INSERT INTO public.notification (user_id, title, message, type, data)
    VALUES (p_user_id, p_title, p_message, p_type, p_data)
    RETURNING id INTO notification_id;
    
    RETURN notification_id;
END;
$$ LANGUAGE plpgsql;

-- Function to handle product completion and winner selection
CREATE OR REPLACE FUNCTION complete_product_raffle(product_id UUID)
RETURNS UUID AS $$
DECLARE
    winner_ticket_id UUID;
    winner_user_id UUID;
    total_tickets INTEGER;
BEGIN
    -- Get total tickets for the product
    SELECT COUNT(*) INTO total_tickets
    FROM public.ticket
    WHERE ticket.product_id = complete_product_raffle.product_id;
    
    -- If no tickets, cannot complete raffle
    IF total_tickets = 0 THEN
        RAISE EXCEPTION 'Cannot complete raffle with no tickets sold';
    END IF;
    
    -- Select random winner
    SELECT id, user_id INTO winner_ticket_id, winner_user_id
    FROM public.ticket
    WHERE ticket.product_id = complete_product_raffle.product_id
    ORDER BY RANDOM()
    LIMIT 1;
    
    -- Update the winning ticket
    UPDATE public.ticket
    SET is_winner = true
    WHERE id = winner_ticket_id;
    
    -- Update the product
    UPDATE public.product
    SET 
        status = 'completed',
        raffle_drawn = true,
        winner_id = winner_user_id,
        draw_date = NOW(),
        updated_at = NOW()
    WHERE id = complete_product_raffle.product_id;
    
    -- Create notification for winner
    PERFORM create_notification(
        winner_user_id,
        'Congratulations! You won!',
        'You have won a raffle! Check your tickets to see which product you won.',
        'raffle_win',
        jsonb_build_object('product_id', complete_product_raffle.product_id, 'ticket_id', winner_ticket_id)
    );
    
    RETURN winner_user_id;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_user_updated_at ON public."user";
CREATE TRIGGER update_user_updated_at BEFORE UPDATE ON public."user"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_category_updated_at ON public.category;
CREATE TRIGGER update_category_updated_at BEFORE UPDATE ON public.category
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_subcategory_updated_at ON public.subcategory;
CREATE TRIGGER update_subcategory_updated_at BEFORE UPDATE ON public.subcategory
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_product_updated_at ON public.product;
CREATE TRIGGER update_product_updated_at BEFORE UPDATE ON public.product
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payment_updated_at ON public.payment;
CREATE TRIGGER update_payment_updated_at BEFORE UPDATE ON public.payment
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_comment_updated_at ON public.comment;
CREATE TRIGGER update_comment_updated_at BEFORE UPDATE ON public.comment
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_notification_updated_at ON public.notification;
CREATE TRIGGER update_notification_updated_at BEFORE UPDATE ON public.notification
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_message_updated_at ON public.message;
CREATE TRIGGER update_message_updated_at BEFORE UPDATE ON public.message
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_charity_updated_at ON public.charity;
CREATE TRIGGER update_charity_updated_at BEFORE UPDATE ON public.charity
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create triggers for ticket management
DROP TRIGGER IF EXISTS validate_ticket_purchase_trigger ON public.ticket;
CREATE TRIGGER validate_ticket_purchase_trigger
    BEFORE INSERT ON public.ticket
    FOR EACH ROW EXECUTE FUNCTION validate_ticket_purchase();

DROP TRIGGER IF EXISTS assign_ticket_number_trigger ON public.ticket;
CREATE TRIGGER assign_ticket_number_trigger
    BEFORE INSERT ON public.ticket
    FOR EACH ROW EXECUTE FUNCTION assign_ticket_number();

DROP TRIGGER IF EXISTS update_tickets_sold_trigger ON public.ticket;
CREATE TRIGGER update_tickets_sold_trigger
    AFTER INSERT OR DELETE ON public.ticket
    FOR EACH ROW EXECUTE FUNCTION update_tickets_sold();
