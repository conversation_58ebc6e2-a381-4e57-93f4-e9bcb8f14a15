#!/bin/bash

# Tombola V2 Development Clean Script
# This script removes all containers, volumes, and networks

set -e

echo "🧹 Cleaning Tombola V2 Development Environment..."
echo "⚠️  This will remove all data including the database!"

read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Operation cancelled."
    exit 1
fi

# Stop and remove everything
echo "🛑 Stopping all services..."
docker-compose down -v --remove-orphans

# Remove images
echo "🗑️  Removing Docker images..."
docker-compose down --rmi all

# Remove unused volumes
echo "🗑️  Removing unused volumes..."
docker volume prune -f

# Remove unused networks
echo "🗑️  Removing unused networks..."
docker network prune -f

echo "✅ Environment cleaned successfully!"
echo ""
echo "🚀 To start fresh, run: ./scripts/dev-start.sh"
