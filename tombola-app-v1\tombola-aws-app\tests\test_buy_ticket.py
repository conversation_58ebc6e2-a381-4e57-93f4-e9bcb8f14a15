from lambda_functions.buy_ticket.handler import handler

def test_buy_ticket_success(monkeypatch):
    def mock_stripe_create(**kwargs):
        class MockIntent:
            id = "pi_test123"
        return MockIntent()

    def mock_insert(data):
        class MockResponse:
            def execute(self): return None
        return MockResponse()

    monkeypatch.setattr("lambda_functions.buy_ticket.handler.stripe.PaymentIntent.create", mock_stripe_create)
    monkeypatch.setattr("lambda_functions.buy_ticket.handler.supabase.table", lambda name: type('', (), {'insert': lambda self, d: mock_insert(d)})())

    event = {"ticket_cost": 5, "payment_method_id": "pm_123", "product_id": "abc-123", "user_id": "u1"}
    result = handler(event, {})
    assert result["statusCode"] == 200