const AWS = require('aws-sdk');
const logger = require('../utils/logger');

class AWSService {
  constructor() {
    // Configure AWS SDK
    AWS.config.update({
      region: process.env.AWS_REGION || 'us-east-1',
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
    });

    this.lambda = new AWS.Lambda();
    this.sns = new AWS.SNS();

    // Lambda function names (should match your CloudFormation stack)
    this.functions = {
      BUY_TICKET: process.env.AWS_LAMBDA_BUY_TICKET || 'tombola-BuyTicketFunction',
      DRAW_RAFFLE: process.env.AWS_LAMBDA_DRAW_RAFFLE || 'tombola-DrawRaffleFunction',
      CONFIRM_DELIVERY: process.env.AWS_LAMBDA_CONFIRM_DELIVERY || 'tombola-ConfirmDeliveryFunction',
      RELEASE_ESCROW: process.env.AWS_LAMBDA_RELEASE_ESCROW || 'tombola-ReleaseEscrowFunction',
      NOTIFICATIONS: process.env.AWS_LAMBDA_NOTIFICATIONS || 'tombola-NotificationsFunction'
    };

    // SNS Topic ARNs
    this.topics = {
      WINNER_SELECTED: process.env.AWS_SNS_WINNER_SELECTED,
      USER_CONFIRMED_DELIVERY: process.env.AWS_SNS_USER_CONFIRMED_DELIVERY,
      ESCROW_RELEASED: process.env.AWS_SNS_ESCROW_RELEASED
    };
  }

  /**
   * Invoke a Lambda function
   * @param {string} functionName - Name of the Lambda function
   * @param {object} payload - Payload to send to the function
   * @param {boolean} async - Whether to invoke asynchronously
   * @returns {Promise<object>} Lambda response
   */
  async invokeLambda(functionName, payload, async = false) {
    try {
      const params = {
        FunctionName: functionName,
        Payload: JSON.stringify(payload),
        InvocationType: async ? 'Event' : 'RequestResponse'
      };

      logger.info(`Invoking Lambda function: ${functionName}`, { payload });

      const result = await this.lambda.invoke(params).promise();

      if (result.FunctionError) {
        throw new Error(`Lambda function error: ${result.FunctionError}`);
      }

      // Parse response for synchronous invocations
      if (!async && result.Payload) {
        const response = JSON.parse(result.Payload);
        
        // Handle Lambda function errors
        if (response.statusCode && response.statusCode >= 400) {
          const errorBody = typeof response.body === 'string' 
            ? JSON.parse(response.body) 
            : response.body;
          throw new Error(errorBody.error || 'Lambda function returned error');
        }

        return response;
      }

      return result;
    } catch (error) {
      logger.error(`Failed to invoke Lambda function ${functionName}:`, error);
      throw new Error(`Failed to invoke Lambda function: ${error.message}`);
    }
  }

  /**
   * Buy a ticket by invoking the buy_ticket Lambda function
   * @param {object} ticketData - Ticket purchase data
   * @returns {Promise<object>} Purchase result
   */
  async buyTicket(ticketData) {
    try {
      const response = await this.invokeLambda(this.functions.BUY_TICKET, ticketData);
      
      // Parse the response body if it's a string
      const result = typeof response.body === 'string' 
        ? JSON.parse(response.body) 
        : response.body || response;

      logger.info('Ticket purchase completed', { 
        productId: ticketData.product_id,
        userId: ticketData.user_id,
        paymentIntentId: result.payment_intent_id
      });

      return result;
    } catch (error) {
      logger.error('Failed to buy ticket:', error);
      throw error;
    }
  }

  /**
   * Draw a raffle by invoking the draw_raffle Lambda function
   * @param {string} productId - Product ID for the raffle
   * @returns {Promise<object>} Draw result
   */
  async drawRaffle(productId) {
    try {
      const response = await this.invokeLambda(this.functions.DRAW_RAFFLE, { product_id: productId });
      
      const result = typeof response.body === 'string' 
        ? JSON.parse(response.body) 
        : response.body || response;

      logger.info('Raffle drawn', { 
        productId,
        winner: result.winner
      });

      return result;
    } catch (error) {
      logger.error('Failed to draw raffle:', error);
      throw error;
    }
  }

  /**
   * Confirm delivery status
   * @param {string} productId - Product ID
   * @param {string} status - Delivery status ('product_sent' or 'product_delivered')
   * @returns {Promise<object>} Confirmation result
   */
  async confirmDelivery(productId, status) {
    try {
      const response = await this.invokeLambda(this.functions.CONFIRM_DELIVERY, {
        product_id: productId,
        status: status
      });

      const result = typeof response.body === 'string' 
        ? JSON.parse(response.body) 
        : response.body || response;

      logger.info('Delivery confirmed', { productId, status });

      return result;
    } catch (error) {
      logger.error('Failed to confirm delivery:', error);
      throw error;
    }
  }

  /**
   * Publish a message to SNS topic
   * @param {string} topicArn - SNS topic ARN
   * @param {object} message - Message to publish
   * @param {string} subject - Message subject
   * @returns {Promise<object>} SNS response
   */
  async publishToSNS(topicArn, message, subject = null) {
    try {
      const params = {
        TopicArn: topicArn,
        Message: typeof message === 'string' ? message : JSON.stringify(message)
      };

      if (subject) {
        params.Subject = subject;
      }

      const result = await this.sns.publish(params).promise();
      
      logger.info('Message published to SNS', { 
        topicArn, 
        messageId: result.MessageId 
      });

      return result;
    } catch (error) {
      logger.error('Failed to publish to SNS:', error);
      throw new Error(`Failed to publish to SNS: ${error.message}`);
    }
  }

  /**
   * Trigger winner notification
   * @param {string} productId - Product ID
   * @param {string} winnerId - Winner user ID
   * @param {string} winnerEmail - Winner email
   * @returns {Promise<object>} SNS response
   */
  async notifyWinner(productId, winnerId, winnerEmail) {
    try {
      const message = {
        product_id: productId,
        winner_id: winnerId,
        winner_email: winnerEmail
      };

      return await this.publishToSNS(
        this.topics.WINNER_SELECTED,
        message,
        'WinnerSelected'
      );
    } catch (error) {
      logger.error('Failed to notify winner:', error);
      throw error;
    }
  }

  /**
   * Trigger escrow release notification
   * @param {string} productId - Product ID
   * @returns {Promise<object>} SNS response
   */
  async notifyEscrowRelease(productId) {
    try {
      const message = {
        product_id: productId
      };

      return await this.publishToSNS(
        this.topics.ESCROW_RELEASED,
        message,
        'EscrowReleased'
      );
    } catch (error) {
      logger.error('Failed to notify escrow release:', error);
      throw error;
    }
  }

  /**
   * Get Lambda function logs (for debugging)
   * @param {string} functionName - Lambda function name
   * @param {number} limit - Number of log events to retrieve
   * @returns {Promise<object>} CloudWatch logs
   */
  async getLambdaLogs(functionName, limit = 50) {
    try {
      const cloudWatchLogs = new AWS.CloudWatchLogs();
      
      const logGroupName = `/aws/lambda/${functionName}`;
      
      const params = {
        logGroupName: logGroupName,
        limit: limit,
        orderBy: 'LastEventTime',
        descending: true
      };

      const result = await cloudWatchLogs.describeLogStreams(params).promise();
      
      return result.logStreams;
    } catch (error) {
      logger.error('Failed to get Lambda logs:', error);
      throw new Error(`Failed to get Lambda logs: ${error.message}`);
    }
  }
}

module.exports = new AWSService();
