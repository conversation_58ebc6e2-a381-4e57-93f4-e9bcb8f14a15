create table "public"."category" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "name" character varying,
    "description" text
);


alter table "public"."category" enable row level security;

create table "public"."charity membership" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "user_id" uuid,
    "charity_id" uuid
);


alter table "public"."charity membership" enable row level security;

create table "public"."charity" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "name" character varying,
    "description" text,
    "logo" character varying,
    "business_address" text
);


alter table "public"."charity" enable row level security;

create table "public"."comment" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "content" text,
    "user_id" uuid,
    "product_id" uuid
);


alter table "public"."comment" enable row level security;

create table "public"."follow" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "follower_id" uuid,
    "followee_id" uuid
);


alter table "public"."follow" enable row level security;

create table "public"."notification" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "user_id" uuid default gen_random_uuid(),
    "message" text,
    "read" boolean
);


alter table "public"."notification" enable row level security;

create table "public"."product" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "user_id" uuid default gen_random_uuid(),
    "product_name" character varying,
    "description" text,
    "price" double precision,
    "ticket_cost" double precision,
    "ticket_count" double precision,
    "raffle_drawn" boolean,
    "product_sent" boolean,
    "product_delivered" boolean
);


alter table "public"."product" enable row level security;

create table "public"."subcategory" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "name" character varying,
    "description" text,
    "category_id" uuid
);


alter table "public"."subcategory" enable row level security;

create table "public"."ticket" (
    "id" uuid not null default gen_random_uuid(),
    "created_at" timestamp with time zone not null default now(),
    "purchase_date" timestamp without time zone default now(),
    "user_id" uuid,
    "product_id" uuid
);


alter table "public"."ticket" enable row level security;

create table "public"."user" (
    "id" uuid not null default auth.uid(),
    "created_at" timestamp with time zone not null default now(),
    "username" character varying,
    "email" character varying,
    "profile_picture" character varying,
    "bio" text,
    "role" text
);


alter table "public"."user" enable row level security;

CREATE UNIQUE INDEX categories_pkey ON public.category USING btree (id);

CREATE UNIQUE INDEX "charity membership_pkey" ON public."charity membership" USING btree (id);

CREATE UNIQUE INDEX charity_pkey ON public.charity USING btree (id);

CREATE UNIQUE INDEX comment_pkey ON public.comment USING btree (id);

CREATE UNIQUE INDEX follow_pkey ON public.follow USING btree (id);

CREATE UNIQUE INDEX notifications_pkey ON public.notification USING btree (id);

CREATE UNIQUE INDEX products_pkey ON public.product USING btree (id);

CREATE UNIQUE INDEX subcategories_pkey ON public.subcategory USING btree (id);

CREATE UNIQUE INDEX ticket_pkey ON public.ticket USING btree (id);

CREATE UNIQUE INDEX users_pkey ON public."user" USING btree (id);

alter table "public"."category" add constraint "categories_pkey" PRIMARY KEY using index "categories_pkey";

alter table "public"."charity membership" add constraint "charity membership_pkey" PRIMARY KEY using index "charity membership_pkey";

alter table "public"."charity" add constraint "charity_pkey" PRIMARY KEY using index "charity_pkey";

alter table "public"."comment" add constraint "comment_pkey" PRIMARY KEY using index "comment_pkey";

alter table "public"."follow" add constraint "follow_pkey" PRIMARY KEY using index "follow_pkey";

alter table "public"."notification" add constraint "notifications_pkey" PRIMARY KEY using index "notifications_pkey";

alter table "public"."product" add constraint "products_pkey" PRIMARY KEY using index "products_pkey";

alter table "public"."subcategory" add constraint "subcategories_pkey" PRIMARY KEY using index "subcategories_pkey";

alter table "public"."ticket" add constraint "ticket_pkey" PRIMARY KEY using index "ticket_pkey";

alter table "public"."user" add constraint "users_pkey" PRIMARY KEY using index "users_pkey";

alter table "public"."charity membership" add constraint "charity membership_charity_id_fkey" FOREIGN KEY (charity_id) REFERENCES charity(id) not valid;

alter table "public"."charity membership" validate constraint "charity membership_charity_id_fkey";

alter table "public"."charity membership" add constraint "charity membership_user_id_fkey" FOREIGN KEY (user_id) REFERENCES "user"(id) not valid;

alter table "public"."charity membership" validate constraint "charity membership_user_id_fkey";

alter table "public"."comment" add constraint "comment_product_id_fkey" FOREIGN KEY (product_id) REFERENCES product(id) not valid;

alter table "public"."comment" validate constraint "comment_product_id_fkey";

alter table "public"."comment" add constraint "comment_user_id_fkey" FOREIGN KEY (user_id) REFERENCES "user"(id) not valid;

alter table "public"."comment" validate constraint "comment_user_id_fkey";

alter table "public"."follow" add constraint "follow_followee_id_fkey" FOREIGN KEY (followee_id) REFERENCES "user"(id) not valid;

alter table "public"."follow" validate constraint "follow_followee_id_fkey";

alter table "public"."follow" add constraint "follow_follower_id_fkey" FOREIGN KEY (follower_id) REFERENCES "user"(id) not valid;

alter table "public"."follow" validate constraint "follow_follower_id_fkey";

alter table "public"."notification" add constraint "notifications_user_id_fkey" FOREIGN KEY (user_id) REFERENCES "user"(id) not valid;

alter table "public"."notification" validate constraint "notifications_user_id_fkey";

alter table "public"."product" add constraint "products_user_id_fkey" FOREIGN KEY (user_id) REFERENCES "user"(id) not valid;

alter table "public"."product" validate constraint "products_user_id_fkey";

alter table "public"."subcategory" add constraint "subcategories_category_id_fkey" FOREIGN KEY (category_id) REFERENCES category(id) not valid;

alter table "public"."subcategory" validate constraint "subcategories_category_id_fkey";

alter table "public"."ticket" add constraint "ticket_product_id_fkey" FOREIGN KEY (product_id) REFERENCES product(id) not valid;

alter table "public"."ticket" validate constraint "ticket_product_id_fkey";

alter table "public"."ticket" add constraint "ticket_user_id_fkey" FOREIGN KEY (user_id) REFERENCES "user"(id) not valid;

alter table "public"."ticket" validate constraint "ticket_user_id_fkey";

grant delete on table "public"."category" to "anon";

grant insert on table "public"."category" to "anon";

grant references on table "public"."category" to "anon";

grant select on table "public"."category" to "anon";

grant trigger on table "public"."category" to "anon";

grant truncate on table "public"."category" to "anon";

grant update on table "public"."category" to "anon";

grant delete on table "public"."category" to "authenticated";

grant insert on table "public"."category" to "authenticated";

grant references on table "public"."category" to "authenticated";

grant select on table "public"."category" to "authenticated";

grant trigger on table "public"."category" to "authenticated";

grant truncate on table "public"."category" to "authenticated";

grant update on table "public"."category" to "authenticated";

grant delete on table "public"."category" to "service_role";

grant insert on table "public"."category" to "service_role";

grant references on table "public"."category" to "service_role";

grant select on table "public"."category" to "service_role";

grant trigger on table "public"."category" to "service_role";

grant truncate on table "public"."category" to "service_role";

grant update on table "public"."category" to "service_role";

grant delete on table "public"."charity membership" to "anon";

grant insert on table "public"."charity membership" to "anon";

grant references on table "public"."charity membership" to "anon";

grant select on table "public"."charity membership" to "anon";

grant trigger on table "public"."charity membership" to "anon";

grant truncate on table "public"."charity membership" to "anon";

grant update on table "public"."charity membership" to "anon";

grant delete on table "public"."charity membership" to "authenticated";

grant insert on table "public"."charity membership" to "authenticated";

grant references on table "public"."charity membership" to "authenticated";

grant select on table "public"."charity membership" to "authenticated";

grant trigger on table "public"."charity membership" to "authenticated";

grant truncate on table "public"."charity membership" to "authenticated";

grant update on table "public"."charity membership" to "authenticated";

grant delete on table "public"."charity membership" to "service_role";

grant insert on table "public"."charity membership" to "service_role";

grant references on table "public"."charity membership" to "service_role";

grant select on table "public"."charity membership" to "service_role";

grant trigger on table "public"."charity membership" to "service_role";

grant truncate on table "public"."charity membership" to "service_role";

grant update on table "public"."charity membership" to "service_role";

grant delete on table "public"."charity" to "anon";

grant insert on table "public"."charity" to "anon";

grant references on table "public"."charity" to "anon";

grant select on table "public"."charity" to "anon";

grant trigger on table "public"."charity" to "anon";

grant truncate on table "public"."charity" to "anon";

grant update on table "public"."charity" to "anon";

grant delete on table "public"."charity" to "authenticated";

grant insert on table "public"."charity" to "authenticated";

grant references on table "public"."charity" to "authenticated";

grant select on table "public"."charity" to "authenticated";

grant trigger on table "public"."charity" to "authenticated";

grant truncate on table "public"."charity" to "authenticated";

grant update on table "public"."charity" to "authenticated";

grant delete on table "public"."charity" to "service_role";

grant insert on table "public"."charity" to "service_role";

grant references on table "public"."charity" to "service_role";

grant select on table "public"."charity" to "service_role";

grant trigger on table "public"."charity" to "service_role";

grant truncate on table "public"."charity" to "service_role";

grant update on table "public"."charity" to "service_role";

grant delete on table "public"."comment" to "anon";

grant insert on table "public"."comment" to "anon";

grant references on table "public"."comment" to "anon";

grant select on table "public"."comment" to "anon";

grant trigger on table "public"."comment" to "anon";

grant truncate on table "public"."comment" to "anon";

grant update on table "public"."comment" to "anon";

grant delete on table "public"."comment" to "authenticated";

grant insert on table "public"."comment" to "authenticated";

grant references on table "public"."comment" to "authenticated";

grant select on table "public"."comment" to "authenticated";

grant trigger on table "public"."comment" to "authenticated";

grant truncate on table "public"."comment" to "authenticated";

grant update on table "public"."comment" to "authenticated";

grant delete on table "public"."comment" to "service_role";

grant insert on table "public"."comment" to "service_role";

grant references on table "public"."comment" to "service_role";

grant select on table "public"."comment" to "service_role";

grant trigger on table "public"."comment" to "service_role";

grant truncate on table "public"."comment" to "service_role";

grant update on table "public"."comment" to "service_role";

grant delete on table "public"."follow" to "anon";

grant insert on table "public"."follow" to "anon";

grant references on table "public"."follow" to "anon";

grant select on table "public"."follow" to "anon";

grant trigger on table "public"."follow" to "anon";

grant truncate on table "public"."follow" to "anon";

grant update on table "public"."follow" to "anon";

grant delete on table "public"."follow" to "authenticated";

grant insert on table "public"."follow" to "authenticated";

grant references on table "public"."follow" to "authenticated";

grant select on table "public"."follow" to "authenticated";

grant trigger on table "public"."follow" to "authenticated";

grant truncate on table "public"."follow" to "authenticated";

grant update on table "public"."follow" to "authenticated";

grant delete on table "public"."follow" to "service_role";

grant insert on table "public"."follow" to "service_role";

grant references on table "public"."follow" to "service_role";

grant select on table "public"."follow" to "service_role";

grant trigger on table "public"."follow" to "service_role";

grant truncate on table "public"."follow" to "service_role";

grant update on table "public"."follow" to "service_role";

grant delete on table "public"."notification" to "anon";

grant insert on table "public"."notification" to "anon";

grant references on table "public"."notification" to "anon";

grant select on table "public"."notification" to "anon";

grant trigger on table "public"."notification" to "anon";

grant truncate on table "public"."notification" to "anon";

grant update on table "public"."notification" to "anon";

grant delete on table "public"."notification" to "authenticated";

grant insert on table "public"."notification" to "authenticated";

grant references on table "public"."notification" to "authenticated";

grant select on table "public"."notification" to "authenticated";

grant trigger on table "public"."notification" to "authenticated";

grant truncate on table "public"."notification" to "authenticated";

grant update on table "public"."notification" to "authenticated";

grant delete on table "public"."notification" to "service_role";

grant insert on table "public"."notification" to "service_role";

grant references on table "public"."notification" to "service_role";

grant select on table "public"."notification" to "service_role";

grant trigger on table "public"."notification" to "service_role";

grant truncate on table "public"."notification" to "service_role";

grant update on table "public"."notification" to "service_role";

grant delete on table "public"."product" to "anon";

grant insert on table "public"."product" to "anon";

grant references on table "public"."product" to "anon";

grant select on table "public"."product" to "anon";

grant trigger on table "public"."product" to "anon";

grant truncate on table "public"."product" to "anon";

grant update on table "public"."product" to "anon";

grant delete on table "public"."product" to "authenticated";

grant insert on table "public"."product" to "authenticated";

grant references on table "public"."product" to "authenticated";

grant select on table "public"."product" to "authenticated";

grant trigger on table "public"."product" to "authenticated";

grant truncate on table "public"."product" to "authenticated";

grant update on table "public"."product" to "authenticated";

grant delete on table "public"."product" to "service_role";

grant insert on table "public"."product" to "service_role";

grant references on table "public"."product" to "service_role";

grant select on table "public"."product" to "service_role";

grant trigger on table "public"."product" to "service_role";

grant truncate on table "public"."product" to "service_role";

grant update on table "public"."product" to "service_role";

grant delete on table "public"."subcategory" to "anon";

grant insert on table "public"."subcategory" to "anon";

grant references on table "public"."subcategory" to "anon";

grant select on table "public"."subcategory" to "anon";

grant trigger on table "public"."subcategory" to "anon";

grant truncate on table "public"."subcategory" to "anon";

grant update on table "public"."subcategory" to "anon";

grant delete on table "public"."subcategory" to "authenticated";

grant insert on table "public"."subcategory" to "authenticated";

grant references on table "public"."subcategory" to "authenticated";

grant select on table "public"."subcategory" to "authenticated";

grant trigger on table "public"."subcategory" to "authenticated";

grant truncate on table "public"."subcategory" to "authenticated";

grant update on table "public"."subcategory" to "authenticated";

grant delete on table "public"."subcategory" to "service_role";

grant insert on table "public"."subcategory" to "service_role";

grant references on table "public"."subcategory" to "service_role";

grant select on table "public"."subcategory" to "service_role";

grant trigger on table "public"."subcategory" to "service_role";

grant truncate on table "public"."subcategory" to "service_role";

grant update on table "public"."subcategory" to "service_role";

grant delete on table "public"."ticket" to "anon";

grant insert on table "public"."ticket" to "anon";

grant references on table "public"."ticket" to "anon";

grant select on table "public"."ticket" to "anon";

grant trigger on table "public"."ticket" to "anon";

grant truncate on table "public"."ticket" to "anon";

grant update on table "public"."ticket" to "anon";

grant delete on table "public"."ticket" to "authenticated";

grant insert on table "public"."ticket" to "authenticated";

grant references on table "public"."ticket" to "authenticated";

grant select on table "public"."ticket" to "authenticated";

grant trigger on table "public"."ticket" to "authenticated";

grant truncate on table "public"."ticket" to "authenticated";

grant update on table "public"."ticket" to "authenticated";

grant delete on table "public"."ticket" to "service_role";

grant insert on table "public"."ticket" to "service_role";

grant references on table "public"."ticket" to "service_role";

grant select on table "public"."ticket" to "service_role";

grant trigger on table "public"."ticket" to "service_role";

grant truncate on table "public"."ticket" to "service_role";

grant update on table "public"."ticket" to "service_role";

grant delete on table "public"."user" to "anon";

grant insert on table "public"."user" to "anon";

grant references on table "public"."user" to "anon";

grant select on table "public"."user" to "anon";

grant trigger on table "public"."user" to "anon";

grant truncate on table "public"."user" to "anon";

grant update on table "public"."user" to "anon";

grant delete on table "public"."user" to "authenticated";

grant insert on table "public"."user" to "authenticated";

grant references on table "public"."user" to "authenticated";

grant select on table "public"."user" to "authenticated";

grant trigger on table "public"."user" to "authenticated";

grant truncate on table "public"."user" to "authenticated";

grant update on table "public"."user" to "authenticated";

grant delete on table "public"."user" to "service_role";

grant insert on table "public"."user" to "service_role";

grant references on table "public"."user" to "service_role";

grant select on table "public"."user" to "service_role";

grant trigger on table "public"."user" to "service_role";

grant truncate on table "public"."user" to "service_role";

grant update on table "public"."user" to "service_role";


