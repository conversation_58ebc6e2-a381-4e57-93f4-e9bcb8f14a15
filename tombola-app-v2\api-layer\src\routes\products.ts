import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { Type } from '@sinclair/typebox';
import { supabaseService } from '../services/supabase';
import { optionalAuthMiddleware } from '../middleware/auth';

export default async function productRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
) {
  // Get all products (public route with optional auth)
  fastify.get('/', {
    preHandler: [optionalAuthMiddleware],
    schema: {
      querystring: Type.Object({
        category: Type.Optional(Type.String()),
        status: Type.Optional(Type.String()),
        limit: Type.Optional(Type.Number({ minimum: 1, maximum: 100 })),
        offset: Type.Optional(Type.Number({ minimum: 0 })),
      }),
      response: {
        200: Type.Object({
          products: Type.Array(Type.Object({
            id: Type.String(),
            name: Type.String(),
            description: Type.String(),
            image_url: Type.Union([Type.String(), Type.Null()]),
            retail_price: Type.Number(),
            ticket_price: Type.Number(),
            max_tickets: Type.Number(),
            tickets_sold: Type.Number(),
            status: Type.String(),
          })),
          total: Type.Number(),
        }),
      },
    },
  }, async (request, reply) => {
    const { category, status, limit = 20, offset = 0 } = request.query as {
      category?: string;
      status?: string;
      limit?: number;
      offset?: number;
    };

    const filters = {
      category,
      status: status || 'active',
      limit,
      offset,
    };

    const products = await supabaseService.getProducts(filters);

    reply.send({
      products: products || [],
      total: products?.length || 0,
    });
  });

  // Get single product
  fastify.get('/:id', {
    preHandler: [optionalAuthMiddleware],
    schema: {
      params: Type.Object({
        id: Type.String(),
      }),
      response: {
        200: Type.Object({
          product: Type.Object({
            id: Type.String(),
            name: Type.String(),
            description: Type.String(),
            image_url: Type.Union([Type.String(), Type.Null()]),
            retail_price: Type.Number(),
            ticket_price: Type.Number(),
            max_tickets: Type.Number(),
            tickets_sold: Type.Number(),
            status: Type.String(),
            draw_date: Type.Union([Type.String(), Type.Null()]),
          }),
        }),
      },
    },
  }, async (request, reply) => {
    const { id } = request.params as { id: string };

    const product = await supabaseService.getProduct(id);

    reply.send({
      product,
    });
  });
}
