'use client'
import { CategoryCard } from "@/components/categoryCard";
import { Button } from "@/components/ui/button";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { useCategory } from "@/hooks/useCategory";
import { useParams } from "next/navigation";
export default function CategoryPage() {
  const params = useParams();
  const categoryId = params?.id as string;
  const { category, loading } = useCategory(categoryId ?? "");
  
  return (
    <>
      <Button
        variant="default"
        onClick={() => (window.location.href = "/explore")}
      >
        Back to {category?.name ?? "Explore"}
      </Button>
      <div className="py-8">
        {loading ? (
           <LoadingSpinner />
        ) : category?.subcategories && category?.subcategories.length > 0 ? (
          category?.subcategories.map((subcategory) => (
            <CategoryCard
              key={subcategory.id}
              id={subcategory.id}
              image_url={subcategory.image ?? ""}
              name={subcategory.name ?? ""}
            />
          ))
        ) : (
          <p>No products found for this category.</p>
        )}
      </div>
    </>
  );
}
