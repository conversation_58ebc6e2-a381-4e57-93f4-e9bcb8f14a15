"use client";
import { CategoryWithSubcategories } from "@/types";
import { createClient } from "@/utils/supabase/client";
import { useEffect, useState } from "react";

/**
 * Custom hook to fetch and filter products by categoryId
 * @param 
 * @returns { categories, loading, error }
 */
export function useCategories() {
  const [categories, setCategories] = useState<CategoryWithSubcategories[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClient();

  useEffect(() => {
    console.log("useeffect running")
    const fetchCategories = async () => {
      setLoading(true);
      setError(null);
      try {

        // Fetch categories with related subcategory and user
        const { data, error } = await supabase
          .from("category")
          .select("*, subcategory(id, name)");

        if (error) throw error;
        setCategories(data || []);
      } catch (error: any) {
        console.error("Failed to fetch categories:", error.message);
        setError(error.message || "An unexpected error occurred.");
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return { categories, loading, error };
}
