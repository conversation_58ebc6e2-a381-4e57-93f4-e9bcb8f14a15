{"name": "tombola-frontend", "version": "1.0.0", "description": "Web-first React Native frontend for Tombola Raffle App (mobile support to be added later)", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start --web", "web": "expo start --web", "dev": "expo start --web --dev-client", "build:web": "expo export:web", "preview": "expo export:web && npx serve dist", "test": "jest", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@expo/webpack-config": "^18.0.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@supabase/supabase-js": "^2.38.4", "@tanstack/react-query": "^4.32.6", "axios": "^1.5.0", "expo": "^53.0.12", "expo-constants": "~14.4.2", "expo-linking": "~5.0.2", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.45.4", "react-native": "^0.72.17", "react-native-gesture-handler": "~2.12.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-web": "~0.19.6", "zod": "^3.22.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-dom": "~18.2.14", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "eslint": "^8.45.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-react": "^7.33.1", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.6.1", "jest-expo": "~49.0.0", "typescript": "^5.1.3"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["@testing-library/jest-native/extend-expect"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}, "private": true, "expo": {"name": "Tombola Raffle", "slug": "tombola-raffle", "version": "1.0.0", "orientation": "portrait", "userInterfaceStyle": "light", "platforms": ["web"], "web": {"bundler": "webpack", "output": "static"}, "assetBundlePatterns": ["**/*"]}, "keywords": ["react-native", "expo", "tombola", "raffle", "web", "mobile", "typescript"], "author": "Tombola Team", "license": "MIT"}