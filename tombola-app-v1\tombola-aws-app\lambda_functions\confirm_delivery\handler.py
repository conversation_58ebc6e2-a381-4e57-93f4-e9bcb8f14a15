def handler(event, context):
    from shared.supabase_client import supabase
    from shared.logger import log
    import boto3

    sns = boto3.client("sns")

    try:
        product_id = event["product_id"]
        status = event["status"]  # "product_sent" or "product_delivered"
        supabase.table("product").update({status: True}).eq("id", product_id).execute()

        product = supabase.table("product").select("*").eq("id", product_id).execute().data[0]
        if product["product_sent"] and product["product_delivered"]:
            supabase.table("escrow").update({"status": "ready"}).eq("product_id", product_id).execute()
            sns.publish(
                TopicArn="<UserConfirmedDeliveryTopic>",
                Message=f"User confirmed delivery for {product_id}"
            )
            log("User confirmed delivery", product_id)
        return {"statusCode": 200}
    except Exception as e:
        log("Error in confirm_delivery", str(e))
        return {"statusCode": 500, "error": str(e)}