alter table "public"."subcategory" drop constraint "subcategory_product_id_fkey";

alter table "public"."product" add column "subcategory_id" uuid;

alter table "public"."subcategory" drop column "product_id";

alter table "public"."subcategory" add column "image" text;

alter table "public"."product" add constraint "product_subcategory_id_fkey" FOREIGN KEY (subcategory_id) REFERENCES subcategory(id) not valid;

alter table "public"."product" validate constraint "product_subcategory_id_fkey";

create policy "Policy with security definer functions"
on "public"."category"
as permissive
for all
to authenticated
using (true);


create policy "Policy with security definer functions"
on "public"."product"
as permissive
for all
to authenticated
using (true);