
def handler(event, context):
    from shared.logger import log
    import boto3
    import json

    try:
        sns_record = event["Records"][0]
        subject = sns_record["Sns"]["Subject"]
        message = json.loads(sns_record["Sns"]["Message"])

        product_id = message["product_id"]
        ses = boto3.client("ses")

        if subject == "WinnerSelected":
            winner_email = message.get("winner_email", "<EMAIL>")
            ses.send_email(
                Source="<EMAIL>",
                Destination={"ToAddresses": [winner_email]},
                Message={
                    "Subject": {"Data": "🎉 You've won a raffle!"},
                    "Body": {
                        "Text": {"Data": f"Congrats! You won the raffle for product {product_id}."}
                    }
                }
            )
            log("Winner notified", product_id)

        elif subject == "EscrowReleased":
            seller_email = message.get("seller_email", "<EMAIL>")
            ses.send_email(
                Source="<EMAIL>",
                Destination={"ToAddresses": [seller_email]},
                Message={
                    "Subject": {"Data": "💰 Funds released"},
                    "Body": {
                        "Text": {"Data": f"The funds for product {product_id} have been released."}
                    }
                }
            )
            log("Seller notified", product_id)

        return {"statusCode": 200}
    except Exception as e:
        log("Error in notifications", str(e))
        return {"statusCode": 500, "error": str(e)}