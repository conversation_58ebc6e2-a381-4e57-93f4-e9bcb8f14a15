'use client'
import { ProductWithRelationship } from "@/types";
import { createClient } from "@/utils/supabase/client";
import { useEffect, useState } from "react";

/**
 * Custom hook to fetch and filter products by categoryId
 * @param categoryId - The category ID to filter products
 * @returns { products, loading, error }
 */
export function useProducts(categoryId?: string) {
  const [products, setProducts] = useState<ProductWithRelationship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);
      setError(null);
      try {
        const supabase = createClient();

        // Fetch products with related subcategory and user
        const { data, error } = await supabase
          .from("product")
          .select("*, subcategory(id, name), user_id");
        if (error) throw error;

        // Filter by categoryId if provided
        const filteredProducts = categoryId
          ? data?.filter(
              (product) => product.subcategory?.id?.toString() === categoryId
            )
          : data;

        setProducts(filteredProducts || []);
      } catch (error: any) {
        console.error("Failed to fetch products:", error.message);
        setError(error.message || "An unexpected error occurred.");
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [categoryId]);

  return { products, loading, error };
}
