# aws-backend/lambda_functions/buy_ticket/handler.py

def handler(event, context):
    from shared.appwrite_client import appwrite
    from shared.stripe_client import stripe
    from shared.logger import log
    import json
    import secrets
    from datetime import datetime
    
    try:
        # Extract event data
        product_id = event.get("product_id")
        user_id = event.get("user_id")
        payment_method_id = event.get("payment_method_id")
        ticket_cost = event.get("ticket_cost")
        
        # Validate required fields
        if not all([product_id, user_id, payment_method_id, ticket_cost]):
            return {
                "statusCode": 400,
                "body": json.dumps({
                    "error": "Missing required fields: product_id, user_id, payment_method_id, ticket_cost"
                })
            }
        
        # Get product details from Appwrite
        product = appwrite.get_product(product_id)
        
        if not product:
            return {
                "statusCode": 404,
                "body": json.dumps({"error": "Product not found"})
            }
        
        # Check if product is still available for ticket purchase
        if product.get('status') != 'active':
            return {
                "statusCode": 400,
                "body": json.dumps({"error": "Product is not available for ticket purchase"})
            }
        
        # Check if tickets are still available
        tickets_sold = product.get('tickets_sold', 0)
        ticket_count = product.get('ticket_count', 0)
        
        if tickets_sold >= ticket_count:
            return {
                "statusCode": 400,
                "body": json.dumps({"error": "No tickets available"})
            }
        
        # Generate unique ticket number
        ticket_number = tickets_sold + 1
        
        # Create Stripe Payment Intent with manual capture for escrow
        payment_intent = stripe.PaymentIntent.create(
            amount=int(ticket_cost * 100),  # Convert to cents
            currency="aud",
            payment_method=payment_method_id,
            confirm=True,
            confirmation_method="manual",
            capture_method="manual",  # Hold funds in escrow
            automatic_payment_methods={"enabled": True},
            metadata={
                "product_id": product_id,
                "user_id": user_id,
                "ticket_number": ticket_number
            }
        )
        
        # Create ticket record in Appwrite
        ticket_data = {
            "user_id": user_id,
            "product_id": product_id,
            "payment_intent_id": payment_intent.id,
            "ticket_number": ticket_number,
            "purchase_date": datetime.utcnow().isoformat(),
            "status": "confirmed"
        }
        
        ticket = appwrite.create_ticket(ticket_data)
        
        # Update product tickets_sold count
        appwrite.update_product(product_id, {
            "tickets_sold": ticket_number
        })
        
        # Create payment record
        payment_data = {
            "user_id": user_id,
            "product_id": product_id,
            "ticket_id": ticket['$id'],
            "stripe_payment_intent_id": payment_intent.id,
            "amount": ticket_cost,
            "currency": "AUD",
            "status": "confirmed",
            "created_at": datetime.utcnow().isoformat()
        }
        
        payment = appwrite.create_payment(payment_data)
        
        # Create escrow record if this is the first ticket for this product
        existing_escrow = appwrite.get_escrow_by_product(product_id)
        if not existing_escrow:
            escrow_data = {
                "product_id": product_id,
                "seller_id": product.get('user_id'),
                "total_amount": 0,  # Will be updated as tickets are sold
                "status": "holding",
                "created_at": datetime.utcnow().isoformat()
            }
            appwrite.create_escrow(escrow_data)
        
        # Create notification for user
        notification_data = {
            "user_id": user_id,
            "title": "Ticket Purchased Successfully",
            "message": f"You have successfully purchased ticket #{ticket_number} for {product.get('product_name')}",
            "type": "success",
            "read": False,
            "created_at": datetime.utcnow().isoformat()
        }
        
        appwrite.create_notification(notification_data)
        
        # Check if all tickets are sold and update product status
        if ticket_number >= ticket_count:
            appwrite.update_product(product_id, {
                "status": "sold_out"
            })
            
            # Create notification for seller
            seller_notification = {
                "user_id": product.get('user_id'),
                "title": "All Tickets Sold",
                "message": f"All tickets for {product.get('product_name')} have been sold. The raffle can now be drawn.",
                "type": "info",
                "read": False,
                "created_at": datetime.utcnow().isoformat()
            }
            
            appwrite.create_notification(seller_notification)
        
        log("Ticket purchased successfully", {
            "payment_intent_id": payment_intent.id,
            "ticket_id": ticket['$id'],
            "product_id": product_id,
            "user_id": user_id,
            "ticket_number": ticket_number
        })
        
        return {
            "statusCode": 200,
            "body": json.dumps({
                "success": True,
                "payment_intent_id": payment_intent.id,
                "ticket_id": ticket['$id'],
                "ticket_number": ticket_number,
                "message": "Ticket purchased successfully"
            })
        }
        
    except stripe.error.StripeError as e:
        log("Stripe error in buy_ticket", str(e))
        return {
            "statusCode": 400,
            "body": json.dumps({
                "error": "Payment failed",
                "details": str(e)
            })
        }
        
    except Exception as e:
        log("Error in buy_ticket", str(e))
        return {
            "statusCode": 500,
            "body": json.dumps({
                "error": "Internal server error",
                "details": str(e)
            })
        }
