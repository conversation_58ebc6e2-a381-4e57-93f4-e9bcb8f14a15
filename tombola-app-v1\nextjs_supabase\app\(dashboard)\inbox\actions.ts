'use server'

import { Database } from '@/types/supabase'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export async function getInboxPreviews() {
    try {
        const cookieStore = await cookies()
        const supabase = createServerClient<Database>(
            process.env.NEXT_PUBLIC_SUPABASE_URL!,
            process.env.SUPABASE_KEY!,
            {
                cookies: {
                    get(name: string) {
                        return cookieStore.get(name)?.value
                    },
                    set(name: string, value: string, options: any) {
                        cookieStore.set({ name, value, ...options })
                    },
                    remove(name: string, options: any) {
                        cookieStore.set({ name, value: '', ...options })
                    },
                },
            }
        )

        const { data: { user }, error: userError } = await supabase.auth.getUser()
        if (userError || !user) throw new Error('Not authenticated')

        // First get all messages
        const { data: messages, error: messagesError } = await supabase
            .from('messages')
            .select('*')
            .or(`sender_id.eq.${user.id},receiver_id.eq.${user.id}`)
            .order('created_at', { ascending: false })

        if (messagesError) throw messagesError

        // Get all unique user IDs from the messages
        const userIds = new Set<string>()
        messages?.forEach(message => {
            if (message.sender_id !== user.id) userIds.add(message.sender_id)
            if (message.receiver_id !== user.id) userIds.add(message.receiver_id)
        })

        // Fetch user details for all participants
        const { data: users, error: usersError } = await supabase
            .from('user')
            .select('id, username, profile_picture')
            .in('id', Array.from(userIds))

        if (usersError) throw usersError

        // Create a map of user details for easy lookup
        const userMap = new Map(users?.map(u => [u.id, u]))

        // Process messages to get unique conversations with latest message
        const conversations = new Map()

        messages?.forEach(message => {
            const otherUserId = message.sender_id === user.id ? message.receiver_id : message.sender_id
            const otherUser = userMap.get(otherUserId)
            const conversationId = [message.sender_id, message.receiver_id].sort().join('-')

            if (!conversations.has(conversationId) && otherUser) {
                const profileImageUrl = otherUser.profile_picture
                    ? `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/profile/${otherUser.profile_picture}`
                    : null

                conversations.set(conversationId, {
                    otherUser: {
                        ...otherUser,
                        profile_picture: profileImageUrl
                    },
                    lastMessage: {
                        content: message.content,
                        created_at: message.created_at,
                        is_read: !!message.read_at,
                        is_sent_by_me: message.sender_id === user.id
                    }
                })
            }
        })

        return Array.from(conversations.values())
    } catch (error) {
        console.error('Error getting inbox:', error)
        throw error
    }
}
