drop policy "Policy with security definer functions" on "public"."category";

drop policy "Policy with security definer functions" on "public"."product";

drop policy "Policy with security definer functions" on "public"."user";

alter table "public"."product" drop constraint "product_subcategory_id_fkey";

create table "public"."messages" (
    "id" uuid not null default gen_random_uuid(),
    "sender_id" uuid not null,
    "receiver_id" uuid not null,
    "content" text,
    "image_url" text,
    "created_at" timestamp with time zone not null default timezone('utc'::text, now()),
    "read_at" timestamp with time zone
);


alter table "public"."messages" enable row level security;

alter table "public"."category" drop column "image";

alter table "public"."product" drop column "fulfillment_method";

alter table "public"."product" drop column "product_images";

alter table "public"."product" drop column "subcategory_id";

alter table "public"."product" drop column "tickets_sold";

alter table "public"."product" drop column "winner_id";

alter table "public"."product" alter column "ticket_count" set data type double precision using "ticket_count"::double precision;

alter table "public"."subcategory" drop column "image";

CREATE UNIQUE INDEX messages_pkey ON public.messages USING btree (id);

alter table "public"."messages" add constraint "messages_pkey" PRIMARY KEY using index "messages_pkey";

alter table "public"."messages" add constraint "messages_receiver_id_fkey" FOREIGN KEY (receiver_id) REFERENCES auth.users(id) not valid;

alter table "public"."messages" validate constraint "messages_receiver_id_fkey";

alter table "public"."messages" add constraint "messages_sender_id_fkey" FOREIGN KEY (sender_id) REFERENCES auth.users(id) not valid;

alter table "public"."messages" validate constraint "messages_sender_id_fkey";

grant delete on table "public"."messages" to "anon";

grant insert on table "public"."messages" to "anon";

grant references on table "public"."messages" to "anon";

grant select on table "public"."messages" to "anon";

grant trigger on table "public"."messages" to "anon";

grant truncate on table "public"."messages" to "anon";

grant update on table "public"."messages" to "anon";

grant delete on table "public"."messages" to "authenticated";

grant insert on table "public"."messages" to "authenticated";

grant references on table "public"."messages" to "authenticated";

grant select on table "public"."messages" to "authenticated";

grant trigger on table "public"."messages" to "authenticated";

grant truncate on table "public"."messages" to "authenticated";

grant update on table "public"."messages" to "authenticated";

grant delete on table "public"."messages" to "service_role";

grant insert on table "public"."messages" to "service_role";

grant references on table "public"."messages" to "service_role";

grant select on table "public"."messages" to "service_role";

grant trigger on table "public"."messages" to "service_role";

grant truncate on table "public"."messages" to "service_role";

grant update on table "public"."messages" to "service_role";

create policy "Policy with security definer functions"
on "public"."follow"
as permissive
for all
to authenticated
using (true);


create policy "Policy with security definer functions"
on "public"."messages"
as permissive
for all
to authenticated, anon
using (true);


create policy "Policy with security definer functions"
on "public"."user"
as permissive
for all
to authenticated
using (true);



