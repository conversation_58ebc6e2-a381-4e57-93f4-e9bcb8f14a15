// Tombola V2 Database Types
// Generated TypeScript types for Supabase database schema

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

// Custom enum types
export type UserRole = 'user' | 'admin' | 'moderator'
export type ProductStatus = 'active' | 'completed' | 'cancelled'
export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'refunded'

export interface Database {
  public: {
    Tables: {
      user: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          username: string | null
          email: string | null
          profile_picture: string | null
          bio: string | null
          role: UserRole
          is_active: boolean
          last_login: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          username?: string | null
          email?: string | null
          profile_picture?: string | null
          bio?: string | null
          role?: UserRole
          is_active?: boolean
          last_login?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          username?: string | null
          email?: string | null
          profile_picture?: string | null
          bio?: string | null
          role?: UserRole
          is_active?: boolean
          last_login?: string | null
        }
      }
      category: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          description: string | null
          is_active: boolean
          sort_order: number
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          description?: string | null
          is_active?: boolean
          sort_order?: number
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          description?: string | null
          is_active?: boolean
          sort_order?: number
        }
      }
      subcategory: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          category_id: string
          name: string
          description: string | null
          is_active: boolean
          sort_order: number
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          category_id: string
          name: string
          description?: string | null
          is_active?: boolean
          sort_order?: number
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          category_id?: string
          name?: string
          description?: string | null
          is_active?: boolean
          sort_order?: number
        }
      }
      product: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          user_id: string
          category_id: string | null
          subcategory_id: string | null
          product_name: string
          description: string | null
          price: number
          ticket_cost: number
          ticket_count: number
          tickets_sold: number
          product_images: string[] | null
          status: ProductStatus
          raffle_drawn: boolean
          product_sent: boolean
          product_delivered: boolean
          winner_id: string | null
          draw_date: string | null
          fulfillment_method: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id: string
          category_id?: string | null
          subcategory_id?: string | null
          product_name: string
          description?: string | null
          price: number
          ticket_cost: number
          ticket_count: number
          tickets_sold?: number
          product_images?: string[] | null
          status?: ProductStatus
          raffle_drawn?: boolean
          product_sent?: boolean
          product_delivered?: boolean
          winner_id?: string | null
          draw_date?: string | null
          fulfillment_method?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          category_id?: string | null
          subcategory_id?: string | null
          product_name?: string
          description?: string | null
          price?: number
          ticket_cost?: number
          ticket_count?: number
          tickets_sold?: number
          product_images?: string[] | null
          status?: ProductStatus
          raffle_drawn?: boolean
          product_sent?: boolean
          product_delivered?: boolean
          winner_id?: string | null
          draw_date?: string | null
          fulfillment_method?: string | null
        }
      }
      ticket: {
        Row: {
          id: string
          created_at: string
          purchase_date: string
          user_id: string
          product_id: string
          ticket_number: number
          is_winner: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          purchase_date?: string
          user_id: string
          product_id: string
          ticket_number?: number
          is_winner?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          purchase_date?: string
          user_id?: string
          product_id?: string
          ticket_number?: number
          is_winner?: boolean
        }
      }
      payment: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          user_id: string
          product_id: string
          ticket_ids: string[]
          amount: number
          currency: string
          status: PaymentStatus
          stripe_payment_intent_id: string | null
          stripe_charge_id: string | null
          payment_method: string | null
          processed_at: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id: string
          product_id: string
          ticket_ids: string[]
          amount: number
          currency?: string
          status?: PaymentStatus
          stripe_payment_intent_id?: string | null
          stripe_charge_id?: string | null
          payment_method?: string | null
          processed_at?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          product_id?: string
          ticket_ids?: string[]
          amount?: number
          currency?: string
          status?: PaymentStatus
          stripe_payment_intent_id?: string | null
          stripe_charge_id?: string | null
          payment_method?: string | null
          processed_at?: string | null
        }
      }
      comment: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          content: string
          user_id: string
          product_id: string
          is_active: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          content: string
          user_id: string
          product_id: string
          is_active?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          content?: string
          user_id?: string
          product_id?: string
          is_active?: boolean
        }
      }
      follow: {
        Row: {
          id: string
          created_at: string
          follower_id: string
          followee_id: string
        }
        Insert: {
          id?: string
          created_at?: string
          follower_id: string
          followee_id: string
        }
        Update: {
          id?: string
          created_at?: string
          follower_id?: string
          followee_id?: string
        }
      }
      notification: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          user_id: string
          title: string | null
          message: string
          type: string
          read: boolean
          read_at: string | null
          data: Json | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id: string
          title?: string | null
          message: string
          type?: string
          read?: boolean
          read_at?: string | null
          data?: Json | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          user_id?: string
          title?: string | null
          message?: string
          type?: string
          read?: boolean
          read_at?: string | null
          data?: Json | null
        }
      }
      message: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          sender_id: string
          receiver_id: string
          content: string | null
          image_url: string | null
          read_at: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          sender_id: string
          receiver_id: string
          content?: string | null
          image_url?: string | null
          read_at?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          sender_id?: string
          receiver_id?: string
          content?: string | null
          image_url?: string | null
          read_at?: string | null
        }
      }
      charity: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          description: string | null
          logo: string | null
          business_address: string | null
          website: string | null
          charity_number: string | null
          is_verified: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          description?: string | null
          logo?: string | null
          business_address?: string | null
          website?: string | null
          charity_number?: string | null
          is_verified?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          description?: string | null
          logo?: string | null
          business_address?: string | null
          website?: string | null
          charity_number?: string | null
          is_verified?: boolean
        }
      }
      charity_membership: {
        Row: {
          id: string
          created_at: string
          user_id: string
          charity_id: string
          role: string
          is_active: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          user_id: string
          charity_id: string
          role?: string
          is_active?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          user_id?: string
          charity_id?: string
          role?: string
          is_active?: boolean
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
