# Tombola V2 - Web-First Development

This is the cleaned up and refined version of Tombola V2, optimized for web-first development with future mobile app support.

## 🎯 What Changed

### Package Cleanup
- **API Layer**: Removed AWS SDK, Stripe, multipart upload, and rate limiting packages
- **Frontend**: Removed mobile-specific packages (camera, notifications, image picker, etc.)
- **Scripts**: Simplified to focus on web development workflow

### Docker Optimization
- **New `docker-compose.dev.yml`**: Simplified development setup with only essential Supabase services
- **Updated Dockerfiles**: Optimized for web-first development
- **Removed**: Redis, Nginx proxy, and other production-oriented services from development

### Web-First Configuration
- **Frontend**: Configured to start as web application by default
- **Expo**: Optimized for web bundling with webpack
- **Scripts**: Updated to use simplified development compose file

## 🚀 Quick Start

### Option 1: Full Development Environment
```bash
# Start all services (database, API, frontend, Supabase Studio)
./scripts/dev-start.sh
```

### Option 2: Web Development Only
```bash
# Start only essential services for web development
./scripts/web-dev.sh
```

## 📱 Development URLs

- **Frontend (Web)**: http://localhost:3000
- **API**: http://localhost:3001
- **API Documentation**: http://localhost:3001/docs
- **Supabase Studio**: http://localhost:3010
- **Database**: localhost:5432

## 🛠️ Available Scripts

### API Layer (`/api-layer`)
```bash
npm run dev          # Start development server with hot reload
npm run build        # Build TypeScript to JavaScript
npm run start        # Start production server
npm run test         # Run tests
npm run lint         # Lint code
npm run type-check   # Check TypeScript types
```

### Frontend (`/frontend`)
```bash
npm start            # Start web development server
npm run web          # Start web development server
npm run dev          # Start web with dev client
npm run build:web    # Build for web production
npm run preview      # Build and serve locally
npm run test         # Run tests
npm run lint         # Lint code
npm run type-check   # Check TypeScript types
```

## 🔧 Docker Commands

```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f [service_name]

# Stop services
docker-compose -f docker-compose.dev.yml down

# Restart a service
docker-compose -f docker-compose.dev.yml restart [service_name]

# Access database shell
docker-compose -f docker-compose.dev.yml exec supabase-db psql -U postgres -d postgres

# Access API container
docker-compose -f docker-compose.dev.yml exec api sh

# Access frontend container
docker-compose -f docker-compose.dev.yml exec frontend sh
```

## 📦 Package Structure

### Core Dependencies Kept
- **API**: Fastify, Supabase, JWT, CORS, Helmet, Swagger
- **Frontend**: React Native Web, Expo, Supabase, React Navigation, React Query

### Removed for Web-First
- **API**: AWS SDK, Stripe, file upload handling, rate limiting
- **Frontend**: Camera, notifications, image picker, native device features

## 🔮 Future Mobile Support

When ready to add mobile apps:

1. **Add back mobile packages**:
   ```bash
   cd frontend
   npm install expo-camera expo-notifications expo-image-picker
   ```

2. **Update Expo config** to include iOS/Android platforms

3. **Add mobile-specific scripts**:
   ```bash
   npm run android
   npm run ios
   ```

4. **Update Docker setup** if needed for mobile development

## 🗄️ Database

The project uses Supabase with PostgreSQL. Database migrations are in `/supabase/migrations/`.

### Development Database Access
- **Host**: localhost
- **Port**: 5432
- **Database**: postgres
- **Username**: postgres
- **Password**: postgres

## 🔑 Environment Variables

Key environment variables for development:
- `EXPO_PUBLIC_API_URL`: http://localhost:3001
- `DATABASE_URL`: ***********************************************/postgres
- `SUPABASE_URL`: http://supabase-kong:8000

## 📝 Next Steps

1. **Test the setup**: Run `./scripts/web-dev.sh` and verify all services start
2. **Develop web features**: Focus on web UI/UX first
3. **Add mobile later**: When ready, gradually add back mobile-specific features
4. **Production setup**: Use the full `docker-compose.yml` for production deployment

## 🤝 Contributing

1. Use the web-first development environment
2. Test changes in the web browser first
3. Keep mobile compatibility in mind for future expansion
4. Follow the existing TypeScript and linting standards
