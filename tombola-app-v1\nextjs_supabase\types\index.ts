// Import the generated types
import { Database } from "@/types/supabase"; 


export type Product = Database["public"]["Tables"]["product"]["Row"];
export type User = Database["public"]["Tables"]["user"]["Row"];

export type Category = Database["public"]["Tables"]["category"]["Row"];
export type Subcategory = Database["public"]["Tables"]["subcategory"]["Row"];

export type CategoryWithSubcategories = Category & {
  subcategories: Subcategory[]
}
export type SubcategoryWithCategory = Subcategory & {
  category: Category
}
export type ProductWithRelationship = Product & {
  user: User,
  subcategory: SubcategoryWithCategory,
}