# Tombola Raffle App V2 - Project Structure
## Architecture: React Native + Fastify API + AWS Lambda + Supabase (TypeScript-First)

## 🏗️ High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Native  │    │   Fastify API   │    │  AWS Lambda     │    │    Supabase     │
│   Frontend      │◄──►│   Gateway       │◄──►│  Functions      │◄──►│   PostgreSQL    │
│   (TypeScript)  │    │  (TypeScript)   │    │   (Python)      │    │   + RLS + Auth  │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Design Principles:
1. **Keep AWS Lambda Functions**: Your existing business logic remains unchanged
2. **Add Fastify API Gateway**: TypeScript server for frontend communication
3. **Keep Supabase**: PostgreSQL + RLS + Auth (proven and reliable)
4. **Modernize Frontend**: Next.js → React Native (TypeScript, web-first)
5. **TypeScript Everywhere**: No JS/JSX files, only TS/TSX
6. **Maintain Integrations**: Keep Stripe, SNS, SES as-is

## 📁 Project Structure

```
tombola-app-v2/
├── aws-backend/                     # Keep existing AWS Lambda structure
│   ├── lambda_functions/            # Your existing Lambda functions
│   │   ├── buy_ticket/
│   │   │   ├── handler.py           # Keep existing (talks to Supabase)
│   │   │   └── dockerfile
│   │   ├── draw_raffle/
│   │   ├── confirm_delivery/
│   │   ├── release_escrow/
│   │   ├── notifications/
│   │   └── shared/                  # Keep existing utilities
│   │       ├── supabase_client.py   # Keep existing
│   │       ├── stripe_client.py     # Keep existing
│   │       └── logger.py            # Keep existing
│   ├── template.yaml                # Keep existing CloudFormation
│   ├── events/                      # Keep existing test events
│   └── tests/                       # Keep existing tests
│
├── api-layer/                       # NEW: Fastify TypeScript API Gateway
│   ├── src/
│   │   ├── routes/
│   │   │   ├── auth.ts              # Supabase auth routes
│   │   │   ├── products.ts          # Product CRUD + Lambda calls
│   │   │   ├── tickets.ts           # Ticket purchase + Lambda calls
│   │   │   ├── users.ts             # User management
│   │   │   └── payments.ts          # Payment processing
│   │   ├── middleware/
│   │   │   ├── auth.ts              # JWT validation
│   │   │   ├── validation.ts        # Schema validation
│   │   │   └── errorHandler.ts      # Error handling
│   │   ├── services/
│   │   │   ├── supabase.ts          # Supabase client
│   │   │   ├── aws.ts               # AWS Lambda invocation
│   │   │   └── stripe.ts            # Stripe integration
│   │   ├── types/
│   │   │   ├── api.ts               # API types
│   │   │   ├── database.ts          # Database types
│   │   │   └── auth.ts              # Auth types
│   │   ├── utils/
│   │   │   ├── logger.ts
│   │   │   └── validation.ts
│   │   └── app.ts                   # Main Fastify app
│   ├── package.json
│   ├── tsconfig.json
│   └── Dockerfile
│
├── frontend/                        # React Native (TypeScript, Web-first)
│   ├── src/
│   │   ├── components/
│   │   │   ├── ui/                  # Reusable UI components (.tsx)
│   │   │   ├── forms/               # Form components (.tsx)
│   │   │   └── layout/              # Layout components (.tsx)
│   │   ├── screens/
│   │   │   ├── auth/                # Login, register, etc. (.tsx)
│   │   │   ├── products/            # Product listing, details (.tsx)
│   │   │   ├── tickets/             # Ticket purchasing, history (.tsx)
│   │   │   └── profile/             # User profile, settings (.tsx)
│   │   ├── navigation/
│   │   │   └── AppNavigator.tsx     # Navigation setup
│   │   ├── services/
│   │   │   ├── api.ts               # API client (calls Fastify API)
│   │   │   ├── auth.ts              # Supabase authentication
│   │   │   └── storage.ts           # Local storage
│   │   ├── hooks/
│   │   │   ├── useAuth.ts           # Auth hook
│   │   │   └── useApi.ts            # API hook
│   │   ├── types/                   # TypeScript definitions
│   │   │   ├── api.ts
│   │   │   ├── navigation.ts
│   │   │   └── user.ts
│   │   ├── utils/
│   │   │   ├── constants.ts
│   │   │   └── helpers.ts
│   │   └── App.tsx                  # Main app component
│   ├── package.json
│   ├── tsconfig.json                # TypeScript configuration
│   ├── metro.config.js
│   ├── app.json                     # Expo configuration
│   └── web/                         # Web-specific configs
│       └── webpack.config.js
│
└── infrastructure/                  # Deployment & DevOps
    ├── aws/
    │   ├── cloudformation/          # Your existing AWS templates
    │   └── scripts/                 # Deployment scripts
    ├── docker/
    │   └── docker-compose.yml       # Local development
    └── docs/
        ├── api/
        ├── architecture/
        └── deployment/
```

## Revised Architecture Design

### 1. Keep Your AWS Lambda Microservices
- **Buy Ticket Function**: Updated to use Appwrite instead of Supabase
- **Draw Raffle Function**: Keep existing logic, update database calls
- **Confirm Delivery Function**: Keep existing logic
- **Release Escrow Function**: Keep existing Stripe integration
- **Notifications Function**: Keep existing SES integration

### 2. Add Node.js API Layer
- **Purpose**: Bridge between React Native frontend and AWS Lambda backend
- **Responsibilities**:
  - Authentication & authorization
  - Request validation
  - Rate limiting
  - Response formatting
  - Direct Appwrite operations for simple CRUD
  - AWS Lambda invocation for complex business logic

### 3. Database Migration: Supabase → Appwrite
- Self-hosted Appwrite instance
- Same data models, different database
- Better control and scalability
- Built-in authentication and file storage

### 4. React Native Frontend
- Web-first development with React Native Web
- Future mobile app capability
- Single codebase for multiple platforms
- Modern UI with native performance
